# FunctionNode 使用指南

## 概述

FunctionNode 是视觉脚本系统中用于执行纯函数的基类。它提供了完整的函数执行管理功能，包括输入验证、结果缓存、类型检查、函数签名定义等。

## 主要功能

### 1. 函数签名定义
- 明确的输入参数定义
- 返回值类型定义
- 参数验证规则
- 纯函数标识

### 2. 输入验证
- 类型检查
- 必需参数检查
- 数值范围验证
- 自定义验证器

### 3. 结果缓存
- LRU缓存机制
- 可配置缓存大小
- 纯函数自动缓存
- 缓存统计信息

### 4. 错误处理
- 输入验证错误
- 计算错误捕获
- 详细错误信息

### 5. 性能优化
- 智能缓存
- 避免重复计算
- 执行统计

## 配置选项

```typescript
interface FunctionNodeOptions extends NodeOptions {
  /** 函数名称 */
  functionName?: string;
  /** 函数签名 */
  signature?: FunctionSignature;
  /** 是否启用缓存 */
  enableCache?: boolean;
  /** 缓存大小限制 */
  cacheSize?: number;
  /** 是否启用输入验证 */
  enableValidation?: boolean;
}
```

## 函数签名定义

```typescript
interface FunctionSignature {
  /** 函数名称 */
  name: string;
  /** 函数描述 */
  description?: string;
  /** 输入参数 */
  parameters: FunctionParameter[];
  /** 返回值 */
  returns: FunctionReturn[];
  /** 是否为纯函数 */
  pure?: boolean;
  /** 是否支持缓存 */
  cacheable?: boolean;
}

interface FunctionParameter {
  /** 参数名称 */
  name: string;
  /** 参数类型 */
  type: string;
  /** 是否可选 */
  optional?: boolean;
  /** 默认值 */
  defaultValue?: any;
  /** 参数描述 */
  description?: string;
  /** 验证函数 */
  validator?: (value: any) => boolean;
  /** 最小值（数值类型） */
  min?: number;
  /** 最大值（数值类型） */
  max?: number;
}
```

## 使用方法

### 1. 创建简单函数节点

```typescript
export class StringLengthNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'stringLength',
      description: '计算字符串的长度',
      parameters: [
        {
          name: 'text',
          type: 'string',
          description: '输入文本',
          defaultValue: ''
        }
      ],
      returns: [
        {
          name: 'length',
          type: 'number',
          description: '字符串长度'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });
  }

  protected compute(inputs: Record<string, any>): any {
    const text = inputs.text as string;
    return {
      length: text.length
    };
  }
}
```

### 2. 创建带验证的函数节点

```typescript
export class AverageNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'average',
      description: '计算数组的平均值',
      parameters: [
        {
          name: 'numbers',
          type: 'array',
          description: '数字数组',
          validator: (value: any) => {
            return Array.isArray(value) && 
                   value.every(n => typeof n === 'number');
          }
        }
      ],
      returns: [
        {
          name: 'average',
          type: 'number',
          description: '平均值'
        },
        {
          name: 'count',
          type: 'number',
          description: '数字个数'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });
  }

  protected compute(inputs: Record<string, any>): any {
    const numbers = inputs.numbers as number[];
    
    if (numbers.length === 0) {
      return { average: 0, count: 0 };
    }

    const sum = numbers.reduce((acc, num) => acc + num, 0);
    return {
      average: sum / numbers.length,
      count: numbers.length
    };
  }
}
```

### 3. 创建带范围验证的函数节点

```typescript
export class ClampNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'clamp',
      description: '将数值限制在指定范围内',
      parameters: [
        {
          name: 'value',
          type: 'number',
          description: '输入值'
        },
        {
          name: 'min',
          type: 'number',
          description: '最小值',
          defaultValue: 0
        },
        {
          name: 'max',
          type: 'number',
          description: '最大值',
          defaultValue: 100,
          validator: (value: any, inputs: any) => {
            return value >= inputs.min;
          }
        }
      ],
      returns: [
        {
          name: 'result',
          type: 'number',
          description: '限制后的值'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });
  }

  protected compute(inputs: Record<string, any>): any {
    const value = inputs.value as number;
    const min = inputs.min as number;
    const max = inputs.max as number;

    return {
      result: Math.max(min, Math.min(max, value))
    };
  }
}
```

## 支持的数据类型

- `string`: 字符串类型
- `number`: 数字类型
- `boolean`: 布尔类型
- `object`: 对象类型
- `array`: 数组类型
- `function`: 函数类型
- `any`: 任意类型

## 缓存机制

FunctionNode 使用 LRU（最近最少使用）缓存机制：

```typescript
// 获取缓存统计
const stats = functionNode.getCacheStats();
console.log(`缓存大小: ${stats.size}/${stats.maxSize}`);
console.log(`命中率: ${stats.hitRate * 100}%`);

// 清空缓存
functionNode.clearCache();
```

## 执行统计

```typescript
// 获取执行统计
const stats = functionNode.getExecutionStats();
console.log(`执行次数: ${stats.executionCount}`);
console.log(`缓存启用: ${stats.cacheEnabled}`);
console.log(`验证启用: ${stats.validationEnabled}`);
```

## 动态签名设置

```typescript
// 动态设置函数签名
const newSignature: FunctionSignature = {
  name: 'newFunction',
  parameters: [...],
  returns: [...]
};

functionNode.setSignature(newSignature);
```

## 最佳实践

1. **明确函数签名**: 为每个函数节点定义清晰的签名
2. **启用验证**: 对输入参数进行严格验证
3. **使用缓存**: 对纯函数启用缓存以提高性能
4. **错误处理**: 在compute方法中妥善处理异常情况
5. **类型安全**: 使用TypeScript类型注解确保类型安全

## 示例节点

系统提供了以下示例函数节点：

- **StringLengthNode**: 字符串长度计算
- **AverageNode**: 数组平均值计算
- **StringFormatNode**: 字符串模板格式化
- **ArrayFilterNode**: 数组条件过滤
- **ObjectPropertyNode**: 对象属性提取

这些示例展示了 FunctionNode 的各种使用方式，可以作为开发自定义函数节点的参考。

## 与其他节点类型的区别

- **FunctionNode**: 纯函数，无副作用，支持缓存
- **FlowNode**: 控制执行流程，有副作用
- **AsyncNode**: 异步操作，支持取消和进度报告
- **EventNode**: 事件处理，响应外部事件
