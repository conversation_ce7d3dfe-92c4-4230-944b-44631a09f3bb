/**
 * 场景理解节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from './Node';

export class NLPSceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/understand',
      category: NodeCategory.AI,
      name: '场景理解',
      description: '分析自然语言文本并提取场景信息'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '输入文本',
      optional: false
    });

    this.addSocket({
      name: 'enable_ai',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '启用AI增强',
      defaultValue: false,
      optional: true
    });

    this.addSocket({
      name: 'ai_services',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI服务列表',
      optional: true
    });

    // 输出插槽
    this.addSocket({
      name: 'entities',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '识别的实体'
    });

    this.addSocket({
      name: 'intent',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '意图分类'
    });

    this.addSocket({
      name: 'sentiment',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '情感倾向'
    });

    this.addSocket({
      name: 'keywords',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '关键词列表'
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '推断风格'
    });

    this.addSocket({
      name: 'complexity',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '场景复杂度'
    });

    this.addSocket({
      name: 'spatial_relations',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '空间关系'
    });

    this.addSocket({
      name: 'temporal_context',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '时间上下文'
    });

    this.addSocket({
      name: 'cultural_context',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '文化上下文'
    });

    this.addSocket({
      name: 'emotional_tone',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '情感色调'
    });

    this.addSocket({
      name: 'understanding',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '完整理解结果'
    });

    // 流程插槽
    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '理解成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '理解失败'
    });
  }

  async executeAsync(inputs: Record<string, any>): Promise<any> {
    const text = inputs.text as string;
    const enableAI = inputs.enable_ai as boolean || false;
    const aiServices = inputs.ai_services as string[] || [];

    // 验证输入
    if (!text || text.trim().length === 0) {
      this.setOutputValue('error', '输入文本不能为空');
      this.triggerFlow('error');
      return { success: false, error: '输入文本不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 执行文本理解
      const understanding = await nlpGenerator.understandText(text);

      // 设置所有输出值
      this.setOutputValue('entities', understanding.entities);
      this.setOutputValue('intent', understanding.intent);
      this.setOutputValue('sentiment', understanding.sentiment);
      this.setOutputValue('keywords', understanding.keywords);
      this.setOutputValue('style', understanding.style);
      this.setOutputValue('complexity', understanding.complexity);
      this.setOutputValue('spatial_relations', understanding.spatialRelations);
      this.setOutputValue('temporal_context', understanding.temporalContext);
      this.setOutputValue('cultural_context', understanding.culturalContext);
      this.setOutputValue('emotional_tone', understanding.emotionalTone);
      this.setOutputValue('understanding', understanding);

      this.triggerFlow('success');
      return { success: true, understanding };

    } catch (error: any) {
      console.error('场景理解失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#4CAF50',
      icon: 'brain',
      category: 'AI',
      tags: ['nlp', 'understanding', 'analysis', 'text'],
      examples: [
        {
          name: '基础文本理解',
          description: '分析简单的场景描述',
          inputs: {
            text: '创建一个现代办公室，有桌子和椅子',
            enable_ai: false
          }
        },
        {
          name: 'AI增强理解',
          description: '使用AI服务增强文本理解',
          inputs: {
            text: '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物',
            enable_ai: true,
            ai_services: ['openai_gpt4']
          }
        }
      ]
    };
  }
}
