# 数字人路径编辑系统技术文档

## 概述

数字人路径编辑系统是一个完整的解决方案，用于创建、编辑和管理数字人在3D场景中的移动路径。该系统支持可视化路径编辑、实时预览、多用户协作编辑，以及与数字人AI系统的深度集成。

## 系统架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   编辑器前端     │    │   服务器后端     │    │   引擎核心       │
│                │    │                │    │                │
│ - 路径编辑器     │◄──►│ - 路径API       │◄──►│ - 导航系统       │
│ - 可视化画布     │    │ - 实时同步      │    │ - 路径跟随       │
│ - 属性面板      │    │ - 数据存储      │    │ - 动画映射       │
│ - 预览系统      │    │ - 权限管理      │    │ - 事件系统       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模块

#### 1. 引擎核心模块 (engine/src/navigation)

**主要组件：**
- `AvatarPath`: 路径数据模型
- `PathPoint`: 路径点数据模型
- `PathFollowingComponent`: 路径跟随组件
- `AvatarPathComponent`: 数字人路径组件
- `AvatarPathSystem`: 路径管理系统

**关键特性：**
- 支持多种循环模式（无循环、循环、往返）
- 支持多种插值类型（线性、平滑、贝塞尔、样条）
- 实时路径验证和优化
- 事件驱动的架构设计

#### 2. 编辑器前端模块 (editor/src/components/AvatarPathEditor)

**主要组件：**
- `AvatarPathEditor`: 主编辑器组件
- `PathCanvas`: 3D路径画布
- `PathPointEditor`: 路径点编辑器
- `PathPropertiesPanel`: 路径属性面板
- `PathPreview`: 路径预览组件
- `PathValidator`: 路径验证器

**关键特性：**
- 基于Three.js的3D可视化编辑
- 实时路径预览和动画播放
- 智能路径验证和错误提示
- 响应式设计和无障碍支持

#### 3. 服务器后端模块 (server/src)

**主要组件：**
- `AvatarPath`: 数据模型（MongoDB）
- `AvatarPathService`: 业务逻辑服务
- `AvatarPathController`: API控制器
- `PathSyncService`: 实时同步服务

**关键特性：**
- RESTful API设计
- WebSocket实时同步
- 数据持久化和版本控制
- 权限管理和安全控制

## 核心功能

### 1. 路径创建和编辑

#### 路径数据结构

```typescript
interface AvatarPathData {
  id: string;
  name: string;
  avatarId: string;
  points: PathPointData[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled: boolean;
  totalDuration: number;
  metadata: PathMetadata;
}

interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
  lookAt?: { x: number; y: number; z: number };
  triggers?: PathTriggerData[];
}
```

#### 编辑操作

1. **添加路径点**: 在3D画布中点击添加新的路径点
2. **移动路径点**: 拖拽路径点到新位置
3. **删除路径点**: 选中路径点后删除
4. **编辑属性**: 修改路径点的速度、等待时间、动画等属性
5. **添加触发器**: 为路径点添加对话、动画、声音等触发器

### 2. 路径跟随系统

#### 核心算法

```typescript
// 路径插值计算
getPositionAtTime(time: number): PathResult {
  const segments = this.calculateSegments();
  const currentSegment = this.findSegmentAtTime(time, segments);
  
  switch (this.interpolation) {
    case InterpolationType.LINEAR:
      return this.linearInterpolation(currentSegment, time);
    case InterpolationType.SMOOTH:
      return this.smoothInterpolation(currentSegment, time);
    case InterpolationType.BEZIER:
      return this.bezierInterpolation(currentSegment, time);
    case InterpolationType.SPLINE:
      return this.splineInterpolation(currentSegment, time);
  }
}
```

#### 动画映射

系统支持智能动画映射，根据移动状态自动选择合适的动画：

```typescript
// 动画映射规则
const animationRules = [
  {
    condition: (ctx) => ctx.speed <= 0.1 && ctx.waitTime > 0,
    animation: 'idle',
    priority: 1
  },
  {
    condition: (ctx) => ctx.speed > 0.1 && ctx.speed <= 2.0,
    animation: 'walk',
    priority: 2
  },
  {
    condition: (ctx) => ctx.speed > 2.0,
    animation: 'run',
    priority: 3
  }
];
```

### 3. 实时协作编辑

#### WebSocket通信协议

```typescript
// 路径操作消息
interface PathOperation {
  id: string;
  type: 'addPoint' | 'removePoint' | 'updatePoint' | 'updatePath';
  pathId: string;
  userId: string;
  timestamp: Date;
  data: any;
}

// 用户状态同步
interface UserState {
  userId: string;
  userName: string;
  cursor?: { x: number; y: number; z: number };
  selection?: { pointIndex: number };
}
```

#### 冲突解决机制

1. **操作时间戳**: 所有操作都带有精确的时间戳
2. **操作队列**: 服务器维护操作队列，按时间顺序处理
3. **状态同步**: 定期同步完整状态，确保一致性
4. **回滚机制**: 支持操作回滚和重做

### 4. 路径验证系统

#### 验证规则

```typescript
interface ValidationRule {
  name: string;
  check: (path: AvatarPath) => ValidationResult;
  severity: 'error' | 'warning' | 'suggestion';
}

const validationRules = [
  {
    name: 'minimumPoints',
    check: (path) => path.points.length >= 2,
    severity: 'error'
  },
  {
    name: 'validSpeed',
    check: (path) => path.points.every(p => p.speed > 0),
    severity: 'error'
  },
  {
    name: 'pathLength',
    check: (path) => path.calculateLength() > 0,
    severity: 'warning'
  }
];
```

#### 性能评估

系统会评估路径的性能影响：

- **复杂度分析**: 计算路径的几何复杂度
- **内存使用**: 估算路径数据的内存占用
- **渲染成本**: 评估路径渲染的性能开销
- **FPS影响**: 预测对帧率的影响

## API 接口

### RESTful API

#### 路径管理

```http
# 创建路径
POST /api/avatar-paths
Content-Type: application/json

{
  "name": "巡逻路径",
  "avatarId": "avatar_001",
  "projectId": "project_001",
  "points": [...],
  "loopMode": "loop",
  "interpolation": "smooth"
}

# 获取路径列表
GET /api/avatar-paths?projectId=project_001&page=1&limit=20

# 获取路径详情
GET /api/avatar-paths/{pathId}

# 更新路径
PUT /api/avatar-paths/{pathId}

# 删除路径
DELETE /api/avatar-paths/{pathId}

# 克隆路径
POST /api/avatar-paths/{pathId}/clone

# 验证路径
POST /api/avatar-paths/{pathId}/validate

# 导出路径
GET /api/avatar-paths/{pathId}/export

# 导入路径
POST /api/avatar-paths/import

# 批量操作
POST /api/avatar-paths/batch
```

#### 统计信息

```http
# 获取项目路径统计
GET /api/avatar-paths/projects/{projectId}/statistics

# 响应示例
{
  "totalPaths": 25,
  "enabledPaths": 20,
  "disabledPaths": 5,
  "averageDuration": 45.2,
  "averageLength": 120.5,
  "totalPoints": 150,
  "pathsByLoopMode": {
    "none": 10,
    "loop": 12,
    "pingpong": 3
  }
}
```

### WebSocket API

#### 连接和认证

```typescript
// 连接到路径编辑会话
socket.emit('joinPathEdit', {
  pathId: 'path_001',
  userId: 'user_001',
  userName: '张三'
});

// 离开编辑会话
socket.emit('leavePathEdit', {
  pathId: 'path_001'
});
```

#### 实时操作

```typescript
// 发送路径操作
socket.emit('pathOperation', {
  type: 'addPoint',
  pathId: 'path_001',
  data: {
    point: {
      position: { x: 10, y: 0, z: 5 },
      speed: 1.5,
      waitTime: 2,
      animation: 'idle'
    }
  }
});

// 接收操作广播
socket.on('pathOperation', (operation) => {
  // 应用其他用户的操作
  applyOperation(operation);
});
```

## 性能优化

### 1. 前端优化

#### 渲染优化
- **LOD系统**: 根据距离调整路径点的详细程度
- **视锥剔除**: 只渲染可见的路径段
- **批量渲染**: 合并多个路径的渲染调用
- **缓存机制**: 缓存计算结果和渲染数据

#### 内存管理
- **对象池**: 重用路径点和线段对象
- **懒加载**: 按需加载路径数据
- **垃圾回收**: 及时清理不用的资源

### 2. 后端优化

#### 数据库优化
- **索引策略**: 为常用查询字段建立索引
- **分页查询**: 避免一次性加载大量数据
- **缓存层**: 使用Redis缓存热点数据
- **连接池**: 优化数据库连接管理

#### 实时同步优化
- **消息压缩**: 压缩WebSocket消息
- **批量操作**: 合并多个小操作
- **增量同步**: 只同步变化的部分
- **连接管理**: 优化WebSocket连接

### 3. 算法优化

#### 路径计算优化
- **预计算**: 预先计算路径段信息
- **插值缓存**: 缓存插值计算结果
- **并行计算**: 利用Web Workers进行并行计算
- **近似算法**: 在精度要求不高时使用近似算法

## 部署和配置

### 环境要求

#### 前端环境
- Node.js 16+
- React 18+
- TypeScript 4.8+
- Three.js 0.150+

#### 后端环境
- Node.js 16+
- MongoDB 5.0+
- Redis 6.0+
- Socket.IO 4.0+

### 配置文件

#### 前端配置 (editor/config/path-editor.json)
```json
{
  "canvas": {
    "defaultGridSize": 1,
    "maxZoom": 10,
    "minZoom": 0.1,
    "enableAntialiasing": true
  },
  "validation": {
    "enableRealtime": true,
    "maxPathPoints": 1000,
    "maxPathLength": 10000
  },
  "collaboration": {
    "enableRealtime": true,
    "maxConcurrentUsers": 10,
    "syncInterval": 100
  }
}
```

#### 后端配置 (server/config/path-service.json)
```json
{
  "database": {
    "mongodb": {
      "url": "mongodb://localhost:27017/avatar_paths",
      "options": {
        "maxPoolSize": 10,
        "serverSelectionTimeoutMS": 5000
      }
    },
    "redis": {
      "url": "redis://localhost:6379",
      "keyPrefix": "avatar_path:"
    }
  },
  "websocket": {
    "cors": {
      "origin": "*",
      "methods": ["GET", "POST"]
    },
    "maxConnections": 1000
  },
  "validation": {
    "enableStrictMode": false,
    "maxOperationsPerSecond": 100
  }
}
```

### 部署步骤

1. **环境准备**
   ```bash
   # 安装依赖
   npm install
   
   # 构建前端
   npm run build:editor
   
   # 构建引擎
   npm run build:engine
   ```

2. **数据库初始化**
   ```bash
   # 启动MongoDB
   mongod --dbpath /data/db
   
   # 启动Redis
   redis-server
   
   # 初始化数据库
   npm run db:init
   ```

3. **服务启动**
   ```bash
   # 启动后端服务
   npm run start:server
   
   # 启动前端服务
   npm run start:editor
   ```

## 故障排除

### 常见问题

#### 1. 路径跟随不流畅
**原因**: 插值计算频率过低或路径点间距过大
**解决**: 增加插值频率，优化路径点分布

#### 2. 实时同步延迟
**原因**: WebSocket连接不稳定或服务器负载过高
**解决**: 检查网络连接，优化服务器性能

#### 3. 路径验证失败
**原因**: 路径数据格式错误或违反验证规则
**解决**: 检查路径数据格式，修复验证错误

#### 4. 内存泄漏
**原因**: 事件监听器未正确清理或对象引用未释放
**解决**: 确保组件销毁时清理所有资源

### 调试工具

#### 1. 路径调试器
```typescript
// 启用路径调试
const pathDebugger = new PathDebugger({
  showWaypoints: true,
  showDirection: true,
  showSpeed: true,
  logEvents: true
});

pathDebugger.attachToPath(avatarPath);
```

#### 2. 性能监控
```typescript
// 性能监控
const monitor = new PathPerformanceMonitor();
monitor.startMonitoring();

// 获取性能报告
const report = monitor.getReport();
console.log('平均帧率:', report.averageFPS);
console.log('内存使用:', report.memoryUsage);
```

## 扩展开发

### 自定义插值算法

```typescript
// 实现自定义插值算法
class CustomInterpolator implements PathInterpolator {
  interpolate(
    point1: PathPoint,
    point2: PathPoint,
    t: number
  ): PathResult {
    // 自定义插值逻辑
    const position = this.customInterpolation(point1, point2, t);
    const rotation = this.calculateRotation(point1, point2, t);
    
    return {
      position,
      rotation,
      animation: this.selectAnimation(point1, point2, t),
      speed: this.interpolateSpeed(point1, point2, t)
    };
  }
}

// 注册自定义插值器
PathInterpolatorRegistry.register('custom', CustomInterpolator);
```

### 自定义验证规则

```typescript
// 添加自定义验证规则
const customRule: ValidationRule = {
  name: 'customPathRule',
  check: (path: AvatarPath) => {
    // 自定义验证逻辑
    const isValid = path.points.every(point => 
      point.position.y >= 0 // 确保所有点都在地面以上
    );
    
    return {
      valid: isValid,
      message: isValid ? '' : '路径点不能在地面以下'
    };
  },
  severity: 'error'
};

PathValidator.addRule(customRule);
```

### 自定义触发器

```typescript
// 实现自定义触发器
class CustomTrigger implements PathTrigger {
  type = 'custom';
  
  execute(data: any, context: TriggerContext): void {
    // 自定义触发器逻辑
    console.log('执行自定义触发器:', data);
    
    // 可以调用外部API、播放特效等
    this.callExternalAPI(data);
    this.playEffect(context.position);
  }
}

// 注册自定义触发器
TriggerRegistry.register('custom', CustomTrigger);
```

## 总结

数字人路径编辑系统提供了一个完整的解决方案，从可视化编辑到实时跟随，从多用户协作到性能优化，涵盖了数字人路径管理的各个方面。系统采用模块化设计，易于扩展和维护，能够满足不同场景下的需求。

通过合理的架构设计和性能优化，系统能够支持大规模的数字人路径管理，为虚拟世界中的数字人提供流畅、自然的移动体验。
