/**
 * AI服务注册节点
 */
import { Node, NodeCategory, SocketDirection, SocketType } from './Node';

export class NLPAIServiceNode extends Node {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/ai/register',
      category: NodeCategory.AI,
      name: '注册AI服务',
      description: '注册外部AI服务用于场景生成增强'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'service_name',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '服务名称',
      optional: false
    });

    this.addSocket({
      name: 'endpoint',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: 'API端点',
      optional: false
    });

    this.addSocket({
      name: 'api_key',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: 'API密钥',
      optional: true
    });

    this.addSocket({
      name: 'model',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '模型名称',
      defaultValue: 'default',
      optional: true
    });

    this.addSocket({
      name: 'capabilities',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI能力列表',
      optional: true
    });

    this.addSocket({
      name: 'rate_limits',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '速率限制配置',
      optional: true
    });

    this.addSocket({
      name: 'fallback_service',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '备用服务名称',
      optional: true
    });

    // 输出插槽
    this.addSocket({
      name: 'service_config',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: 'AI服务配置'
    });

    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册失败'
    });
  }

  execute(inputs: Record<string, any>): any {
    const serviceName = inputs.service_name as string;
    const endpoint = inputs.endpoint as string;
    const apiKey = inputs.api_key as string;
    const model = inputs.model as string || 'default';
    const capabilities = inputs.capabilities as string[] || ['text_understanding'];
    const rateLimits = inputs.rate_limits as any;
    const fallbackService = inputs.fallback_service as string;

    // 验证输入
    if (!serviceName || serviceName.trim().length === 0) {
      this.setOutputValue('error', '服务名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '服务名称不能为空' };
    }

    if (!endpoint || endpoint.trim().length === 0) {
      this.setOutputValue('error', 'API端点不能为空');
      this.triggerFlow('error');
      return { success: false, error: 'API端点不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 构建AI服务配置
      const serviceConfig = {
        name: serviceName,
        endpoint,
        apiKey,
        model,
        capabilities: this.parseCapabilities(capabilities),
        rateLimits: rateLimits || {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          maxConcurrent: 5
        },
        fallbackService
      };

      // 注册AI服务
      nlpGenerator.registerAIService(serviceConfig);

      // 设置输出值
      this.setOutputValue('service_config', serviceConfig);

      this.triggerFlow('success');
      return { success: true, serviceConfig };

    } catch (error: any) {
      console.error('注册AI服务失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  private parseCapabilities(capabilities: string[]): string[] {
    const validCapabilities = [
      'text_understanding',
      'scene_planning',
      'object_generation',
      'style_transfer',
      'optimization'
    ];

    return capabilities.filter(cap => validCapabilities.includes(cap));
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#2196F3',
      icon: 'cloud',
      category: 'AI',
      tags: ['nlp', 'ai', 'service', 'register'],
      examples: [
        {
          name: '注册OpenAI服务',
          description: '注册OpenAI GPT服务',
          inputs: {
            service_name: 'openai_gpt4',
            endpoint: 'https://api.openai.com/v1',
            model: 'gpt-4',
            capabilities: ['text_understanding', 'scene_planning'],
            rate_limits: {
              requestsPerMinute: 60,
              requestsPerHour: 1000,
              maxConcurrent: 5
            }
          }
        },
        {
          name: '注册本地NLP服务',
          description: '注册本地NLP服务',
          inputs: {
            service_name: 'local_bert',
            endpoint: 'http://localhost:8080/api',
            model: 'bert-base-chinese',
            capabilities: ['text_understanding'],
            rate_limits: {
              requestsPerMinute: 120,
              requestsPerHour: 5000,
              maxConcurrent: 10
            },
            fallback_service: 'openai_gpt4'
          }
        }
      ]
    };
  }
}
