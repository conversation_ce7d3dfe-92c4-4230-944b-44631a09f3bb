/**
 * 函数节点示例
 * 展示如何使用FunctionNode基类创建具体的函数节点
 */
import { FunctionNode, FunctionNodeOptions, FunctionSignature } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 字符串长度函数节点
 * 计算字符串的长度
 */
export class StringLengthNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'stringLength',
      description: '计算字符串的长度',
      parameters: [
        {
          name: 'text',
          type: 'string',
          description: '输入文本',
          defaultValue: ''
        }
      ],
      returns: [
        {
          name: 'length',
          type: 'number',
          description: '字符串长度'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '字符串长度';
    }
    if (!this.metadata.description) {
      this.metadata.description = '计算字符串的长度';
    }
  }

  protected compute(inputs: Record<string, any>): any {
    const text = inputs.text as string;
    return {
      length: text.length
    };
  }
}

/**
 * 数学平均值函数节点
 * 计算数组的平均值
 */
export class AverageNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'average',
      description: '计算数组的平均值',
      parameters: [
        {
          name: 'numbers',
          type: 'array',
          description: '数字数组',
          validator: (value: any) => {
            return Array.isArray(value) && value.every(n => typeof n === 'number');
          }
        }
      ],
      returns: [
        {
          name: 'average',
          type: 'number',
          description: '平均值'
        },
        {
          name: 'count',
          type: 'number',
          description: '数字个数'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '平均值';
    }
    if (!this.metadata.description) {
      this.metadata.description = '计算数组的平均值';
    }
  }

  protected compute(inputs: Record<string, any>): any {
    const numbers = inputs.numbers as number[];
    
    if (numbers.length === 0) {
      return {
        average: 0,
        count: 0
      };
    }

    const sum = numbers.reduce((acc, num) => acc + num, 0);
    const average = sum / numbers.length;

    return {
      average,
      count: numbers.length
    };
  }
}

/**
 * 字符串格式化函数节点
 * 使用模板格式化字符串
 */
export class StringFormatNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'stringFormat',
      description: '使用模板格式化字符串',
      parameters: [
        {
          name: 'template',
          type: 'string',
          description: '模板字符串（使用 {key} 作为占位符）',
          defaultValue: 'Hello {name}!'
        },
        {
          name: 'values',
          type: 'object',
          description: '替换值对象',
          defaultValue: {}
        }
      ],
      returns: [
        {
          name: 'result',
          type: 'string',
          description: '格式化后的字符串'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '字符串格式化';
    }
    if (!this.metadata.description) {
      this.metadata.description = '使用模板格式化字符串';
    }
  }

  protected compute(inputs: Record<string, any>): any {
    const template = inputs.template as string;
    const values = inputs.values as Record<string, any>;

    let result = template;
    
    // 替换模板中的占位符
    for (const [key, value] of Object.entries(values)) {
      const placeholder = `{${key}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }

    return {
      result
    };
  }
}

/**
 * 数组过滤函数节点
 * 根据条件过滤数组
 */
export class ArrayFilterNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'arrayFilter',
      description: '根据条件过滤数组',
      parameters: [
        {
          name: 'array',
          type: 'array',
          description: '输入数组',
          defaultValue: []
        },
        {
          name: 'property',
          type: 'string',
          description: '要检查的属性名',
          optional: true
        },
        {
          name: 'operator',
          type: 'string',
          description: '比较操作符 (>, <, >=, <=, ==, !=)',
          defaultValue: '=='
        },
        {
          name: 'value',
          type: 'any',
          description: '比较值'
        }
      ],
      returns: [
        {
          name: 'filtered',
          type: 'array',
          description: '过滤后的数组'
        },
        {
          name: 'count',
          type: 'number',
          description: '过滤后的元素个数'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '数组过滤';
    }
    if (!this.metadata.description) {
      this.metadata.description = '根据条件过滤数组';
    }
  }

  protected compute(inputs: Record<string, any>): any {
    const array = inputs.array as any[];
    const property = inputs.property as string;
    const operator = inputs.operator as string;
    const value = inputs.value;

    const filtered = array.filter(item => {
      const itemValue = property ? item[property] : item;
      
      switch (operator) {
        case '>':
          return itemValue > value;
        case '<':
          return itemValue < value;
        case '>=':
          return itemValue >= value;
        case '<=':
          return itemValue <= value;
        case '==':
          return itemValue == value;
        case '!=':
          return itemValue != value;
        case '===':
          return itemValue === value;
        case '!==':
          return itemValue !== value;
        default:
          return itemValue == value;
      }
    });

    return {
      filtered,
      count: filtered.length
    };
  }
}

/**
 * 对象属性提取函数节点
 * 从对象中提取指定属性
 */
export class ObjectPropertyNode extends FunctionNode {
  constructor(options: FunctionNodeOptions) {
    const signature: FunctionSignature = {
      name: 'objectProperty',
      description: '从对象中提取指定属性',
      parameters: [
        {
          name: 'object',
          type: 'object',
          description: '输入对象'
        },
        {
          name: 'path',
          type: 'string',
          description: '属性路径（支持点号分隔的嵌套路径）',
          defaultValue: ''
        },
        {
          name: 'defaultValue',
          type: 'any',
          description: '默认值（当属性不存在时返回）',
          optional: true
        }
      ],
      returns: [
        {
          name: 'value',
          type: 'any',
          description: '属性值'
        },
        {
          name: 'exists',
          type: 'boolean',
          description: '属性是否存在'
        }
      ],
      pure: true,
      cacheable: true
    };

    super({
      ...options,
      signature,
      enableCache: true,
      enableValidation: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '对象属性';
    }
    if (!this.metadata.description) {
      this.metadata.description = '从对象中提取指定属性';
    }
  }

  protected compute(inputs: Record<string, any>): any {
    const object = inputs.object;
    const path = inputs.path as string;
    const defaultValue = inputs.defaultValue;

    if (!path) {
      return {
        value: object,
        exists: true
      };
    }

    const pathParts = path.split('.');
    let current = object;
    let exists = true;

    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        exists = false;
        current = defaultValue;
        break;
      }
    }

    return {
      value: current,
      exists
    };
  }
}

/**
 * 注册函数示例节点
 * @param registry 节点注册表
 */
export function registerFunctionExampleNodes(registry: NodeRegistry): void {
  // 注册字符串长度节点
  registry.registerNodeType({
    type: 'function/string/length',
    category: NodeCategory.FUNCTION,
    constructor: StringLengthNode,
    label: '字符串长度',
    description: '计算字符串的长度',
    icon: 'text',
    color: '#4CAF50',
    tags: ['function', 'string', 'length']
  });

  // 注册平均值节点
  registry.registerNodeType({
    type: 'function/math/average',
    category: NodeCategory.FUNCTION,
    constructor: AverageNode,
    label: '平均值',
    description: '计算数组的平均值',
    icon: 'average',
    color: '#2196F3',
    tags: ['function', 'math', 'average']
  });

  // 注册字符串格式化节点
  registry.registerNodeType({
    type: 'function/string/format',
    category: NodeCategory.FUNCTION,
    constructor: StringFormatNode,
    label: '字符串格式化',
    description: '使用模板格式化字符串',
    icon: 'format',
    color: '#4CAF50',
    tags: ['function', 'string', 'format']
  });

  // 注册数组过滤节点
  registry.registerNodeType({
    type: 'function/array/filter',
    category: NodeCategory.FUNCTION,
    constructor: ArrayFilterNode,
    label: '数组过滤',
    description: '根据条件过滤数组',
    icon: 'filter',
    color: '#FF9800',
    tags: ['function', 'array', 'filter']
  });

  // 注册对象属性节点
  registry.registerNodeType({
    type: 'function/object/property',
    category: NodeCategory.FUNCTION,
    constructor: ObjectPropertyNode,
    label: '对象属性',
    description: '从对象中提取指定属性',
    icon: 'property',
    color: '#9C27B0',
    tags: ['function', 'object', 'property']
  });
}
