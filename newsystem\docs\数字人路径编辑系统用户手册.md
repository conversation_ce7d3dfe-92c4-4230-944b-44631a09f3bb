# 数字人路径编辑系统用户手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [界面介绍](#界面介绍)
4. [基本操作](#基本操作)
5. [高级功能](#高级功能)
6. [协作编辑](#协作编辑)
7. [常见问题](#常见问题)
8. [最佳实践](#最佳实践)

## 系统概述

数字人路径编辑系统是一个专业的3D路径编辑工具，用于创建和管理数字人在虚拟场景中的移动路径。系统支持可视化编辑、实时预览、多用户协作等功能，让您能够轻松创建复杂的数字人行为路径。

### 主要功能

- **可视化路径编辑**: 在3D场景中直观地创建和编辑路径
- **实时预览**: 实时查看数字人沿路径移动的效果
- **智能动画映射**: 根据移动状态自动选择合适的动画
- **路径验证**: 自动检测和修复路径中的问题
- **多用户协作**: 支持多人同时编辑同一路径
- **导入导出**: 支持路径数据的导入和导出

### 系统要求

- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **硬件**: 支持WebGL的显卡，4GB以上内存
- **网络**: 稳定的网络连接（协作功能需要）

## 快速开始

### 1. 登录系统

1. 打开浏览器，访问系统地址
2. 输入用户名和密码登录
3. 选择要编辑的项目

### 2. 创建第一个路径

1. 点击"新建路径"按钮
2. 输入路径名称和选择关联的数字人
3. 在3D画布中点击添加路径点
4. 调整路径点属性
5. 点击"保存"完成创建

### 3. 预览路径效果

1. 选择创建的路径
2. 切换到"预览"标签页
3. 点击"播放"按钮查看效果
4. 使用控制面板调整播放速度和视角

## 界面介绍

### 主界面布局

```
┌─────────────┬─────────────────────────┬─────────────────┐
│   场景面板   │       路径列表          │   编辑器面板     │
│            │                        │                │
│ - 实体列表   │ - 路径搜索和过滤         │ - 画布标签页     │
│ - 场景树     │ - 路径状态显示          │ - 属性标签页     │
│ - 图层管理   │ - 批量操作工具          │ - 预览标签页     │
│            │                        │ - 验证标签页     │
└─────────────┴─────────────────────────┴─────────────────┘
```

### 工具栏

- **新建路径**: 创建新的路径
- **导入路径**: 从文件导入路径数据
- **导出路径**: 将路径导出为文件
- **批量操作**: 对多个路径执行批量操作
- **设置**: 系统设置和偏好

### 状态栏

- **连接状态**: 显示网络连接和协作状态
- **性能信息**: 显示帧率和内存使用情况
- **操作提示**: 显示当前操作的提示信息

## 基本操作

### 创建路径

#### 1. 新建路径

1. 点击工具栏的"新建路径"按钮
2. 在弹出的对话框中填写路径信息：
   - **路径名称**: 为路径起一个描述性的名称
   - **关联数字人**: 选择要使用此路径的数字人
   - **描述**: 可选的路径描述信息
   - **标签**: 为路径添加分类标签

3. 点击"创建"按钮完成

#### 2. 添加路径点

1. 在3D画布中点击要添加路径点的位置
2. 路径点会自动添加到路径中
3. 可以通过拖拽调整路径点位置
4. 右键点击路径点可以删除或编辑属性

#### 3. 编辑路径点属性

选中路径点后，在属性面板中可以编辑：

- **位置坐标**: 精确设置路径点的3D坐标
- **等待时间**: 数字人在此点停留的时间（秒）
- **移动速度**: 到达此点时的移动速度（米/秒）
- **动画**: 在此点播放的动画名称
- **朝向目标**: 可选的朝向目标坐标
- **触发器**: 到达此点时触发的事件

### 路径属性设置

#### 基本属性

- **路径名称**: 路径的显示名称
- **关联数字人**: 使用此路径的数字人ID
- **启用状态**: 是否启用此路径
- **描述**: 路径的详细描述
- **标签**: 路径分类标签

#### 行为设置

- **循环模式**:
  - **不循环**: 到达终点后停止
  - **循环**: 到达终点后回到起点继续
  - **往返**: 到达终点后原路返回

- **插值类型**:
  - **线性**: 直线移动，速度均匀
  - **平滑**: 平滑过渡，自然的加减速
  - **贝塞尔**: 贝塞尔曲线插值，更流畅的路径
  - **样条**: 样条曲线插值，最平滑的路径

### 路径编辑操作

#### 选择和移动

- **选择路径点**: 点击路径点进行选择
- **多选**: 按住Ctrl键点击多个路径点
- **移动**: 拖拽选中的路径点到新位置
- **精确移动**: 在属性面板中输入精确坐标

#### 插入和删除

- **插入路径点**: 在路径线上点击插入新的路径点
- **删除路径点**: 选中路径点后按Delete键或右键删除
- **批量删除**: 选中多个路径点后批量删除

#### 复制和粘贴

- **复制路径点**: Ctrl+C复制选中的路径点
- **粘贴路径点**: Ctrl+V粘贴路径点
- **复制路径**: 复制整个路径到新路径

## 高级功能

### 触发器系统

触发器允许在路径点处执行特定的动作，支持以下类型：

#### 1. 对话触发器

在路径点处触发数字人说话：

```
触发器类型: 对话
对话内容: "欢迎来到我们的展厅！"
说话人: 数字人名称
持续时间: 3秒
```

#### 2. 动画触发器

在路径点处播放特定动画：

```
触发器类型: 动画
动画名称: "wave"（挥手）
是否循环: 否
播放速度: 1.0
```

#### 3. 声音触发器

在路径点处播放音效：

```
触发器类型: 声音
音频文件: "welcome.mp3"
音量: 0.8
是否循环: 否
```

#### 4. 事件触发器

在路径点处触发自定义事件：

```
触发器类型: 事件
事件名称: "showInfo"
事件数据: {"type": "product", "id": "001"}
```

### 路径验证

系统会自动验证路径的有效性，检查以下问题：

#### 错误级别问题

- **路径点不足**: 路径至少需要2个路径点
- **无效速度**: 速度必须大于0
- **无效等待时间**: 等待时间不能为负数
- **无效动画**: 动画名称不能为空

#### 警告级别问题

- **路径过短**: 路径长度可能过短
- **速度过快**: 移动速度可能过快，影响真实感
- **等待时间过长**: 等待时间可能过长，影响体验

#### 建议级别问题

- **路径优化**: 建议简化路径以提高性能
- **动画匹配**: 建议使用更合适的动画
- **触发器优化**: 建议优化触发器设置

### 路径优化

#### 自动优化

系统提供自动优化功能：

1. **路径简化**: 移除冗余的路径点
2. **平滑处理**: 平滑路径转角
3. **速度优化**: 优化移动速度分布
4. **动画匹配**: 自动匹配合适的动画

#### 手动优化

1. **合并相近点**: 合并距离很近的路径点
2. **插入中间点**: 在长距离段插入中间点
3. **调整速度**: 根据路径特点调整速度
4. **优化朝向**: 设置合理的朝向目标

### 批量操作

#### 批量编辑路径点

1. 选择多个路径点
2. 在属性面板中修改属性
3. 属性会应用到所有选中的路径点

#### 批量路径操作

1. 在路径列表中选择多个路径
2. 使用批量操作工具：
   - **启用/禁用**: 批量启用或禁用路径
   - **删除**: 批量删除路径
   - **导出**: 批量导出路径
   - **设置分类**: 批量设置路径分类

## 协作编辑

### 加入协作会话

1. 选择要编辑的路径
2. 系统会自动连接到协作会话
3. 可以看到其他用户的光标和选择状态
4. 实时同步所有编辑操作

### 协作功能

#### 实时同步

- **操作同步**: 所有编辑操作实时同步给其他用户
- **光标显示**: 显示其他用户的鼠标光标位置
- **选择状态**: 显示其他用户选中的路径点
- **用户列表**: 显示当前在线的协作用户

#### 冲突处理

- **操作排队**: 冲突操作会按时间顺序排队处理
- **自动合并**: 系统自动合并兼容的操作
- **冲突提示**: 显示操作冲突的提示信息
- **版本控制**: 保存操作历史，支持回滚

### 协作最佳实践

1. **明确分工**: 不同用户编辑不同的路径段
2. **及时沟通**: 使用聊天功能进行沟通
3. **定期保存**: 定期保存工作进度
4. **避免冲突**: 避免同时编辑同一个路径点

## 常见问题

### Q: 路径预览不流畅怎么办？

**A**: 可能的解决方案：
1. 检查浏览器性能，关闭不必要的标签页
2. 降低路径复杂度，减少路径点数量
3. 调整预览质量设置
4. 更新显卡驱动程序

### Q: 无法保存路径怎么办？

**A**: 可能的原因和解决方案：
1. 检查网络连接是否正常
2. 确认路径验证是否通过
3. 检查是否有足够的存储权限
4. 尝试刷新页面重新登录

### Q: 协作编辑延迟很大怎么办？

**A**: 可能的解决方案：
1. 检查网络连接质量
2. 减少同时在线的协作用户数量
3. 关闭不必要的浏览器扩展
4. 联系管理员检查服务器状态

### Q: 路径点无法精确定位怎么办？

**A**: 解决方案：
1. 使用网格对齐功能
2. 在属性面板中输入精确坐标
3. 调整画布缩放级别
4. 使用键盘方向键微调位置

### Q: 动画播放不正确怎么办？

**A**: 检查以下设置：
1. 确认动画名称是否正确
2. 检查数字人模型是否支持该动画
3. 验证动画文件是否存在
4. 检查动画播放参数设置

## 最佳实践

### 路径设计原则

1. **简洁明了**: 路径应该简洁，避免不必要的复杂性
2. **自然流畅**: 路径应该符合自然的移动规律
3. **目标明确**: 每个路径都应该有明确的目的
4. **性能优化**: 考虑路径对性能的影响

### 路径点设置建议

1. **合理间距**: 路径点间距应该适中，不要过密或过疏
2. **速度匹配**: 移动速度应该与路径特点匹配
3. **动画选择**: 选择与移动状态匹配的动画
4. **等待时间**: 合理设置等待时间，增强真实感

### 触发器使用建议

1. **适度使用**: 不要过度使用触发器，避免干扰用户
2. **内容相关**: 触发器内容应该与场景相关
3. **时机合适**: 选择合适的时机触发事件
4. **测试验证**: 充分测试触发器的效果

### 协作编辑建议

1. **提前规划**: 在开始协作前制定编辑计划
2. **分工明确**: 明确每个人的编辑范围
3. **及时沟通**: 遇到问题及时沟通解决
4. **版本管理**: 定期保存重要版本

### 性能优化建议

1. **路径简化**: 定期清理和简化复杂路径
2. **批量操作**: 使用批量操作提高效率
3. **缓存利用**: 合理利用浏览器缓存
4. **资源管理**: 及时清理不用的资源

## 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

1. **在线帮助**: 点击界面右上角的帮助按钮
2. **用户手册**: 查阅完整的用户手册文档
3. **技术支持**: 联系技术支持团队
4. **社区论坛**: 在用户社区中寻求帮助

---

*本手册会根据系统更新持续完善，请关注最新版本。*
