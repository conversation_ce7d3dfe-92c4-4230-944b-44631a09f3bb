/**
 * 自定义风格注册节点
 */
import { Node, NodeCategory, SocketDirection, SocketType } from './Node';

export class NLPCustomStyleNode extends Node {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/style/register',
      category: NodeCategory.AI,
      name: '注册自定义风格',
      description: '注册自定义场景生成风格'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'style_name',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '风格名称',
      optional: false
    });

    this.addSocket({
      name: 'description',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '风格描述',
      optional: true
    });

    this.addSocket({
      name: 'material_presets',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '材质预设列表',
      optional: true
    });

    this.addSocket({
      name: 'lighting_presets',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '光照预设列表',
      optional: true
    });

    this.addSocket({
      name: 'color_palette',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '颜色调色板',
      optional: true
    });

    this.addSocket({
      name: 'atmosphere_settings',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '氛围设置',
      optional: true
    });

    // 输出插槽
    this.addSocket({
      name: 'style_config',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '风格配置对象'
    });

    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册失败'
    });
  }

  execute(inputs: Record<string, any>): any {
    const styleName = inputs.style_name as string;
    const description = inputs.description as string || '';
    const materialPresets = inputs.material_presets as any[] || [];
    const lightingPresets = inputs.lighting_presets as any[] || [];
    const colorPalette = inputs.color_palette as string[] || ['#ffffff', '#000000'];
    const atmosphereSettings = inputs.atmosphere_settings as any || {};

    // 验证输入
    if (!styleName || styleName.trim().length === 0) {
      this.setOutputValue('error', '风格名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '风格名称不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 构建风格配置
      const styleConfig = {
        name: styleName,
        description,
        materialPresets,
        lightingPresets,
        objectModifiers: [],
        atmosphereSettings: {
          fogDensity: 0.0,
          fogColor: '#ffffff',
          skyboxType: 'default',
          postProcessingEffects: [],
          ...atmosphereSettings
        },
        colorPalette
      };

      // 注册自定义风格
      nlpGenerator.registerCustomStyle(styleConfig);

      // 设置输出值
      this.setOutputValue('style_config', styleConfig);

      this.triggerFlow('success');
      return { success: true, styleConfig };

    } catch (error: any) {
      console.error('注册自定义风格失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#9C27B0',
      icon: 'palette',
      category: 'AI',
      tags: ['nlp', 'style', 'custom', 'register'],
      examples: [
        {
          name: '注册现代风格',
          description: '注册一个现代简约风格',
          inputs: {
            style_name: 'modern_minimal',
            description: '现代简约风格',
            color_palette: ['#ffffff', '#f5f5f5', '#e0e0e0', '#bdbdbd']
          }
        },
        {
          name: '注册工业风格',
          description: '注册一个工业风格',
          inputs: {
            style_name: 'industrial',
            description: '工业风格',
            color_palette: ['#424242', '#616161', '#757575', '#9e9e9e']
          }
        }
      ]
    };
  }
}
