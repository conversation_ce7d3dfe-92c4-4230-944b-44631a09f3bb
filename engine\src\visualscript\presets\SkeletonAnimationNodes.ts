/**
 * 骨骼动画视觉脚本节点
 * 为骨骼系统和动作合成功能提供节点式开发支持
 */
import { Node } from '../nodes/Node';
import { NodeCategory } from '../nodes/NodeRegistry';
import { SocketType, SocketDirection } from '../nodes/Socket';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Entity } from '../../core/Entity';
import { AutoSkeletonRiggingSystem, AvatarData, SkeletonData } from '../../animation/AutoSkeletonRiggingSystem';
import { MultiActionCompositionSystem, ActionCompositionConfig, ComposedActionSet } from '../../animation/MultiActionCompositionSystem';
import { SynchronizedAnimationSystem, ActionSet, FacialAnimationSet, SynchronizationConfig, SynchronizedAnimationSet } from '../../animation/SynchronizedAnimationSystem';
import * as THREE from 'three';

/**
 * 自动骨骼绑定节点
 */
export class AutoRigAvatarNode extends Node {
  public static readonly TYPE = 'skeleton/autoRigAvatar';

  constructor() {
    super(AutoRigAvatarNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'avatarData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'AvatarData',
      description: '虚拟化身数据'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'onError',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '错误输出'
    });

    this.addOutput({
      name: 'skeletonData',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'SkeletonData',
      description: '骨骼数据'
    });

    this.addOutput({
      name: 'qualityScore',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '质量评分'
    });
  }

  protected async executeImpl(): Promise<any> {
    const avatarData = this.getInputValue('avatarData') as AvatarData;

    if (!avatarData) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: '缺少虚拟化身数据' };
    }

    try {
      // 获取自动骨骼绑定系统
      const riggingSystem = new AutoSkeletonRiggingSystem();
      
      // 执行自动骨骼绑定
      const skeletonData = await riggingSystem.autoRigAvatar(avatarData);

      // 设置输出值
      this.setOutputValue('skeletonData', skeletonData);
      this.setOutputValue('qualityScore', skeletonData.qualityScore);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return { success: true, skeletonData };
    } catch (error) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 多动作合成节点
 */
export class ComposeMultipleActionsNode extends Node {
  public static readonly TYPE = 'skeleton/composeMultipleActions';

  constructor() {
    super(ComposeMultipleActionsNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'avatarId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '虚拟化身ID'
    });

    this.addInput({
      name: 'actionFiles',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'File[]',
      description: '动作文件数组'
    });

    this.addInput({
      name: 'config',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'ActionCompositionConfig',
      description: '合成配置'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'onError',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '错误输出'
    });

    this.addOutput({
      name: 'composedActionSet',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'ComposedActionSet',
      description: '合成的动作集合'
    });

    this.addOutput({
      name: 'actionCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '动作数量'
    });
  }

  protected async executeImpl(): Promise<any> {
    const avatarId = this.getInputValue('avatarId') as string;
    const actionFiles = this.getInputValue('actionFiles') as File[];
    const config = this.getInputValue('config') as ActionCompositionConfig;

    if (!avatarId || !actionFiles || !config) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: '缺少必要参数' };
    }

    try {
      // 获取多动作合成系统
      const compositionSystem = new MultiActionCompositionSystem();
      
      // 执行多动作合成
      const composedActionSet = await compositionSystem.composeMultipleActions(
        avatarId,
        actionFiles,
        config
      );

      // 设置输出值
      this.setOutputValue('composedActionSet', composedActionSet);
      this.setOutputValue('actionCount', composedActionSet.actions.length);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return { success: true, composedActionSet };
    } catch (error) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 同步面部动画节点
 */
export class SynchronizeFacialAnimationNode extends Node {
  public static readonly TYPE = 'skeleton/synchronizeFacialAnimation';

  constructor() {
    super(SynchronizeFacialAnimationNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'bodyActions',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'ActionSet',
      description: '身体动作集合'
    });

    this.addInput({
      name: 'facialAnimations',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'FacialAnimationSet',
      description: '面部动画集合'
    });

    this.addInput({
      name: 'syncConfig',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'SynchronizationConfig',
      description: '同步配置'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'onError',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '错误输出'
    });

    this.addOutput({
      name: 'synchronizedAnimations',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'SynchronizedAnimationSet',
      description: '同步的动画集合'
    });

    this.addOutput({
      name: 'qualityMetrics',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '质量指标'
    });
  }

  protected async executeImpl(): Promise<any> {
    const bodyActions = this.getInputValue('bodyActions') as ActionSet;
    const facialAnimations = this.getInputValue('facialAnimations') as FacialAnimationSet;
    const syncConfig = this.getInputValue('syncConfig') as SynchronizationConfig;

    if (!bodyActions || !facialAnimations || !syncConfig) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: '缺少必要参数' };
    }

    try {
      // 获取同步动画系统
      const syncSystem = new SynchronizedAnimationSystem();
      
      // 执行动画同步
      const synchronizedAnimations = await syncSystem.synchronizeAnimations(
        bodyActions,
        facialAnimations,
        syncConfig
      );

      // 设置输出值
      this.setOutputValue('synchronizedAnimations', synchronizedAnimations);
      this.setOutputValue('qualityMetrics', synchronizedAnimations.qualityMetrics);

      // 触发完成流程
      this.triggerFlow('onComplete');

      return { success: true, synchronizedAnimations };
    } catch (error) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 创建虚拟化身数据节点
 */
export class CreateAvatarDataNode extends Node {
  public static readonly TYPE = 'skeleton/createAvatarData';

  constructor() {
    super(CreateAvatarDataNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'bodyGeometry',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'BufferGeometry',
      description: '身体几何体'
    });

    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '名称',
      defaultValue: 'Avatar'
    });

    this.addInput({
      name: 'gender',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '性别',
      defaultValue: 'unisex'
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '身高',
      defaultValue: 1.7
    });

    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '体重',
      defaultValue: 70
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'avatarData',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'AvatarData',
      description: '虚拟化身数据'
    });
  }

  protected executeImpl(): any {
    const bodyGeometry = this.getInputValue('bodyGeometry') as THREE.BufferGeometry;
    const name = this.getInputValue('name') as string;
    const gender = this.getInputValue('gender') as 'male' | 'female' | 'unisex';
    const height = this.getInputValue('height') as number;
    const weight = this.getInputValue('weight') as number;

    if (!bodyGeometry) {
      return { success: false, error: '缺少身体几何体' };
    }

    const avatarData: AvatarData = {
      bodyData: {
        geometry: bodyGeometry
      },
      metadata: {
        name,
        gender,
        height,
        weight
      }
    };

    this.setOutputValue('avatarData', avatarData);
    this.triggerFlow('onComplete');

    return { success: true, avatarData };
  }
}

/**
 * 获取骨骼信息节点
 */
export class GetSkeletonInfoNode extends Node {
  public static readonly TYPE = 'skeleton/getSkeletonInfo';

  constructor() {
    super(GetSkeletonInfoNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'skeletonData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'SkeletonData',
      description: '骨骼数据'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'boneCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '骨骼数量'
    });

    this.addOutput({
      name: 'boneNames',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string[]',
      description: '骨骼名称列表'
    });

    this.addOutput({
      name: 'qualityScore',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '质量评分'
    });
  }

  protected executeImpl(): any {
    const skeletonData = this.getInputValue('skeletonData') as SkeletonData;

    if (!skeletonData) {
      return { success: false, error: '缺少骨骼数据' };
    }

    const boneCount = skeletonData.skeleton.bones.length;
    const boneNames = skeletonData.skeleton.bones.map(bone => bone.name);
    const qualityScore = skeletonData.qualityScore;

    this.setOutputValue('boneCount', boneCount);
    this.setOutputValue('boneNames', boneNames);
    this.setOutputValue('qualityScore', qualityScore);
    this.triggerFlow('onComplete');

    return { success: true, boneCount, boneNames, qualityScore };
  }
}

/**
 * 应用骨骼到实体节点
 */
export class ApplySkeletonToEntityNode extends Node {
  public static readonly TYPE = 'skeleton/applySkeletonToEntity';

  constructor() {
    super(ApplySkeletonToEntityNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Entity',
      description: '目标实体'
    });

    this.addInput({
      name: 'skeletonData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'SkeletonData',
      description: '骨骼数据'
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'onError',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '错误输出'
    });

    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '应用骨骼后的实体'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const skeletonData = this.getInputValue('skeletonData') as SkeletonData;

    if (!entity || !skeletonData) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: '缺少必要参数' };
    }

    try {
      // 这里应该将骨骼数据应用到实体
      // 由于涉及复杂的组件系统，暂时简化处理

      this.setOutputValue('entity', entity);
      this.triggerFlow('onComplete');

      return { success: true, entity };
    } catch (error) {
      this.setOutputValue('onError', true);
      this.triggerFlow('onError');
      return { success: false, error: error.message };
    }
  }
}

/**
 * 创建动作合成配置节点
 */
export class CreateActionCompositionConfigNode extends Node {
  public static readonly TYPE = 'skeleton/createActionCompositionConfig';

  constructor() {
    super(CreateActionCompositionConfigNode.TYPE);
    this.initializeSockets();
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'execute',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    this.addInput({
      name: 'targetSkeleton',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Skeleton',
      description: '目标骨骼结构'
    });

    this.addInput({
      name: 'defaultDuration',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '默认过渡时间',
      defaultValue: 0.3
    });

    this.addInput({
      name: 'smoothingFactor',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '平滑因子',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'minQualityThreshold',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '最小质量阈值',
      defaultValue: 0.7
    });

    // 输出插槽
    this.addOutput({
      name: 'onComplete',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成输出'
    });

    this.addOutput({
      name: 'config',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'ActionCompositionConfig',
      description: '动作合成配置'
    });
  }

  protected executeImpl(): any {
    const targetSkeleton = this.getInputValue('targetSkeleton') as THREE.Skeleton;
    const defaultDuration = this.getInputValue('defaultDuration') as number;
    const smoothingFactor = this.getInputValue('smoothingFactor') as number;
    const minQualityThreshold = this.getInputValue('minQualityThreshold') as number;

    if (!targetSkeleton) {
      return { success: false, error: '缺少目标骨骼结构' };
    }

    const config: ActionCompositionConfig = {
      targetSkeleton,
      transitionRules: {
        defaultDuration,
        smoothingFactor,
        blendMode: 'linear'
      },
      qualitySettings: {
        minQualityThreshold,
        enableOptimization: true,
        compressionLevel: 0.8
      }
    };

    this.setOutputValue('config', config);
    this.triggerFlow('onComplete');

    return { success: true, config };
  }
}

/**
 * 注册骨骼动画节点
 * @param registry 节点注册表
 */
export function registerSkeletonAnimationNodes(registry: NodeRegistry): void {
  // 注册自动骨骼绑定节点
  registry.registerNodeType({
    type: AutoRigAvatarNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: AutoRigAvatarNode,
    label: '自动骨骼绑定',
    description: '为虚拟化身自动添加骨骼结构',
    icon: 'skeleton',
    color: '#FF9800',
    tags: ['skeleton', 'rigging', 'avatar']
  });

  // 注册多动作合成节点
  registry.registerNodeType({
    type: ComposeMultipleActionsNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: ComposeMultipleActionsNode,
    label: '多动作合成',
    description: '合成多个动作文件',
    icon: 'compose',
    color: '#FF9800',
    tags: ['animation', 'composition', 'actions']
  });

  // 注册面部动画同步节点
  registry.registerNodeType({
    type: SynchronizeFacialAnimationNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: SynchronizeFacialAnimationNode,
    label: '同步面部动画',
    description: '同步面部动画和身体动作',
    icon: 'sync',
    color: '#FF9800',
    tags: ['facial', 'animation', 'sync']
  });

  // 注册创建虚拟化身数据节点
  registry.registerNodeType({
    type: CreateAvatarDataNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: CreateAvatarDataNode,
    label: '创建虚拟化身数据',
    description: '创建虚拟化身数据结构',
    icon: 'avatar',
    color: '#FF9800',
    tags: ['avatar', 'data', 'create']
  });

  // 注册获取骨骼信息节点
  registry.registerNodeType({
    type: GetSkeletonInfoNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: GetSkeletonInfoNode,
    label: '获取骨骼信息',
    description: '获取骨骼结构的详细信息',
    icon: 'info',
    color: '#FF9800',
    tags: ['skeleton', 'info', 'bones']
  });

  // 注册应用骨骼到实体节点
  registry.registerNodeType({
    type: ApplySkeletonToEntityNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: ApplySkeletonToEntityNode,
    label: '应用骨骼到实体',
    description: '将骨骼结构应用到实体',
    icon: 'apply',
    color: '#FF9800',
    tags: ['skeleton', 'entity', 'apply']
  });

  // 注册创建动作合成配置节点
  registry.registerNodeType({
    type: CreateActionCompositionConfigNode.TYPE,
    category: NodeCategory.ANIMATION,
    constructor: CreateActionCompositionConfigNode,
    label: '创建动作合成配置',
    description: '创建动作合成的配置参数',
    icon: 'config',
    color: '#FF9800',
    tags: ['config', 'composition', 'actions']
  });
}
