/**
 * 自然语言场景生成节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from './Node';
import { GenerationOptions } from '../../ai/NLPSceneGenerator';

export class NLPSceneGenerationNode extends AsyncNode {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/scene/generate',
      category: NodeCategory.AI,
      name: '自然语言场景生成',
      description: '基于自然语言描述生成3D场景'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'text',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '场景描述文本',
      optional: false
    });

    this.addSocket({
      name: 'style',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '生成风格',
      defaultValue: 'realistic',
      optional: true
    });

    this.addSocket({
      name: 'quality',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '质量等级 (1-100)',
      defaultValue: 80,
      optional: true
    });

    this.addSocket({
      name: 'maxObjects',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '最大对象数',
      defaultValue: 50,
      optional: true
    });

    this.addSocket({
      name: 'customStyle',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '自定义风格配置',
      optional: true
    });

    this.addSocket({
      name: 'customObjects',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '自定义对象类型列表',
      optional: true
    });

    this.addSocket({
      name: 'aiServices',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI服务列表',
      optional: true
    });

    this.addSocket({
      name: 'enableAdvancedFeatures',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '启用高级功能',
      defaultValue: false,
      optional: true
    });

    this.addSocket({
      name: 'seedValue',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '随机种子值',
      optional: true
    });

    this.addSocket({
      name: 'templateScene',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '模板场景ID',
      optional: true
    });

    // 输出插槽
    this.addSocket({
      name: 'scene',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '生成的场景'
    });

    this.addSocket({
      name: 'understanding',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '语言理解结果'
    });

    this.addSocket({
      name: 'progress',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '生成进度 (0-100)'
    });

    this.addSocket({
      name: 'metadata',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '生成元数据'
    });

    // 流程插槽
    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '生成成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '生成失败'
    });

    this.addSocket({
      name: 'progress_update',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '进度更新'
    });
  }

  async executeAsync(inputs: Record<string, any>): Promise<any> {
    const text = inputs.text as string;
    const style = inputs.style as string || 'realistic';
    const quality = inputs.quality as number || 80;
    const maxObjects = inputs.maxObjects as number || 50;
    const customStyle = inputs.customStyle as any;
    const customObjects = inputs.customObjects as any[];
    const aiServices = inputs.aiServices as string[];
    const enableAdvancedFeatures = inputs.enableAdvancedFeatures as boolean || false;
    const seedValue = inputs.seedValue as number;
    const templateScene = inputs.templateScene as string;

    // 验证输入
    if (!text || text.trim().length === 0) {
      this.setOutputValue('error', '场景描述不能为空');
      this.triggerFlow('error');
      return { success: false, error: '场景描述不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      const options: GenerationOptions = {
        style: style as any,
        quality,
        maxObjects,
        constraints: {
          maxPolygons: quality * 1000,
          targetFrameRate: 60
        },
        onProgress: (progress: number) => {
          this.setOutputValue('progress', progress);
          this.triggerFlow('progress_update');
        },
        customStyle,
        customObjects,
        aiServices,
        enableAdvancedFeatures,
        seedValue,
        templateScene
      };

      // 开始生成
      this.setOutputValue('progress', 0);

      const scene = await nlpGenerator.generateSceneFromNaturalLanguage(text, options);

      // 设置输出值
      this.setOutputValue('scene', scene);
      this.setOutputValue('progress', 100);

      // 如果有理解结果，也输出
      if ((scene as any).understanding) {
        this.setOutputValue('understanding', (scene as any).understanding);
      }

      // 设置增强的元数据
      this.setOutputValue('metadata', {
        generationTime: Date.now(),
        objectCount: (scene as any).entities?.length || 0,
        style,
        quality,
        customFeaturesUsed: {
          customStyle: !!customStyle,
          customObjects: !!customObjects,
          aiServices: aiServices?.length || 0,
          advancedFeatures: enableAdvancedFeatures,
          seedValue: !!seedValue,
          templateScene: !!templateScene
        }
      });

      this.triggerFlow('success');
      return { success: true, scene };

    } catch (error: any) {
      console.error('自然语言场景生成失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#722ED1',
      icon: 'robot',
      category: 'AI',
      tags: ['nlp', 'scene', 'generation', 'ai'],
      examples: [
        {
          name: '基础场景生成',
          description: '使用简单描述生成场景',
          inputs: {
            text: '创建一个现代办公室',
            style: 'realistic',
            quality: 70,
            maxObjects: 30
          }
        },
        {
          name: '复杂场景生成',
          description: '使用详细描述生成复杂场景',
          inputs: {
            text: '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物',
            style: 'realistic',
            quality: 90,
            maxObjects: 60
          }
        },
        {
          name: '自定义风格场景',
          description: '使用自定义风格生成场景',
          inputs: {
            text: '创建一个工业风格的工厂车间',
            style: 'industrial',
            quality: 85,
            maxObjects: 40,
            enableAdvancedFeatures: true
          }
        },
        {
          name: 'AI增强生成',
          description: '使用AI服务增强场景生成',
          inputs: {
            text: '创建一个科幻实验室，要有未来感和高科技设备',
            style: 'scifi',
            quality: 95,
            maxObjects: 50,
            aiServices: ['openai_gpt4'],
            enableAdvancedFeatures: true
          }
        },
        {
          name: '可重现生成',
          description: '使用固定种子生成可重现的场景',
          inputs: {
            text: '创建一个魔法森林',
            style: 'fantasy',
            quality: 80,
            maxObjects: 45,
            seedValue: 12345,
            enableAdvancedFeatures: true
          }
        }
      ]
    };
  }
}
