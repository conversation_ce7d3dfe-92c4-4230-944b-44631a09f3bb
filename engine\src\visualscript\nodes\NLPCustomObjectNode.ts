/**
 * 自定义对象类型注册节点
 */
import { Node, NodeCategory, SocketDirection, SocketType } from './Node';

export class NLPCustomObjectNode extends Node {
  constructor(options: any) {
    super({
      ...options,
      type: 'nlp/object/register',
      category: NodeCategory.AI,
      name: '注册自定义对象',
      description: '注册自定义3D对象类型'
    });

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addSocket({
      name: 'object_name',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '对象名称',
      optional: false
    });

    this.addSocket({
      name: 'category',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '对象类别',
      defaultValue: 'custom',
      optional: true
    });

    this.addSocket({
      name: 'description',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '对象描述',
      optional: true
    });

    this.addSocket({
      name: 'geometry_type',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '几何体类型',
      defaultValue: 'box',
      optional: true
    });

    this.addSocket({
      name: 'geometry_params',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '几何体参数',
      optional: true
    });

    this.addSocket({
      name: 'default_material',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'string',
      description: '默认材质',
      defaultValue: 'standard',
      optional: true
    });

    this.addSocket({
      name: 'tags',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'array',
      description: '标签列表',
      optional: true
    });

    this.addSocket({
      name: 'complexity',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'number',
      description: '复杂度 (1-10)',
      defaultValue: 5,
      optional: true
    });

    this.addSocket({
      name: 'bounding_box',
      direction: SocketDirection.INPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '边界框',
      optional: true
    });

    // 输出插槽
    this.addSocket({
      name: 'object_config',
      direction: SocketDirection.OUTPUT,
      type: SocketType.DATA,
      dataType: 'object',
      description: '对象配置'
    });

    this.addSocket({
      name: 'success',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册成功'
    });

    this.addSocket({
      name: 'error',
      direction: SocketDirection.OUTPUT,
      type: SocketType.FLOW,
      description: '注册失败'
    });
  }

  execute(inputs: Record<string, any>): any {
    const objectName = inputs.object_name as string;
    const category = inputs.category as string || 'custom';
    const description = inputs.description as string || '';
    const geometryType = inputs.geometry_type as string || 'box';
    const geometryParams = inputs.geometry_params as any || {};
    const defaultMaterial = inputs.default_material as string || 'standard';
    const tags = inputs.tags as string[] || [];
    const complexity = Math.max(1, Math.min(10, inputs.complexity as number || 5));
    const boundingBox = inputs.bounding_box as any;

    // 验证输入
    if (!objectName || objectName.trim().length === 0) {
      this.setOutputValue('error', '对象名称不能为空');
      this.triggerFlow('error');
      return { success: false, error: '对象名称不能为空' };
    }

    try {
      const world = this.context.getWorld();
      const nlpGenerator = world.getSystem('NLPSceneGenerator');

      if (!nlpGenerator) {
        throw new Error('自然语言场景生成器未初始化');
      }

      // 创建几何体工厂函数
      const geometryFactory = (params: any) => {
        const mergedParams = { ...geometryParams, ...params };
        
        switch (geometryType) {
          case 'box':
            return {
              type: 'box',
              width: mergedParams.width || 1,
              height: mergedParams.height || 1,
              depth: mergedParams.depth || 1
            };
          case 'sphere':
            return {
              type: 'sphere',
              radius: mergedParams.radius || 0.5
            };
          case 'cylinder':
            return {
              type: 'cylinder',
              radius: mergedParams.radius || 0.5,
              height: mergedParams.height || 1
            };
          case 'cone':
            return {
              type: 'cone',
              radius: mergedParams.radius || 0.5,
              height: mergedParams.height || 1
            };
          default:
            return {
              type: 'box',
              width: 1,
              height: 1,
              depth: 1
            };
        }
      };

      // 构建对象配置
      const objectConfig = {
        name: objectName,
        category,
        description,
        geometryFactory,
        defaultMaterial,
        boundingBox: boundingBox || {
          min: { x: -0.5, y: 0, z: -0.5 },
          max: { x: 0.5, y: 1, z: 0.5 }
        },
        tags: [...tags, category, 'custom'],
        complexity,
        additionalComponents: []
      };

      // 注册自定义对象类型
      nlpGenerator.registerCustomObject(objectConfig);

      // 设置输出值
      this.setOutputValue('object_config', objectConfig);

      this.triggerFlow('success');
      return { success: true, objectConfig };

    } catch (error: any) {
      console.error('注册自定义对象失败:', error);
      this.setOutputValue('error', error.message);
      this.triggerFlow('error');
      return { success: false, error: error.message };
    }
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#FF5722',
      icon: 'cube',
      category: 'AI',
      tags: ['nlp', 'object', 'custom', 'register'],
      examples: [
        {
          name: '注册自定义桌子',
          description: '注册一个自定义桌子对象',
          inputs: {
            object_name: 'custom_table',
            category: 'furniture',
            description: '自定义桌子',
            geometry_type: 'box',
            geometry_params: { width: 2, height: 0.1, depth: 1 },
            tags: ['furniture', 'table'],
            complexity: 3
          }
        },
        {
          name: '注册装饰球',
          description: '注册一个装饰球对象',
          inputs: {
            object_name: 'decoration_sphere',
            category: 'decoration',
            description: '装饰球体',
            geometry_type: 'sphere',
            geometry_params: { radius: 0.3 },
            tags: ['decoration', 'sphere'],
            complexity: 2
          }
        }
      ]
    };
  }
}
