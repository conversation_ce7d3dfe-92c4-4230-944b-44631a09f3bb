/**
 * 数字人路径组件
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { AvatarPath } from '../path/AvatarPath';
import { PathFollowingComponent } from './PathFollowingComponent';
import { AvatarPathOptions } from '../types';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 数字人路径组件
 */
export class AvatarPathComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'AvatarPathComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 路径列表 */
  private paths: Map<string, AvatarPath> = new Map();
  /** 当前活动路径 */
  private activePath: AvatarPath | null = null;
  /** 路径跟随组件 */
  private pathFollowingComponent: PathFollowingComponent | null = null;
  /** 是否自动开始 */
  private autoStart: boolean = false;
  /** 是否启用调试 */
  private debug: boolean = false;
  /** 路径平滑度 */
  private smoothness: number = 1.0;
  /** 最大速度 */
  private maxSpeed: number = 10.0;
  /** 最小速度 */
  private minSpeed: number = 0.1;

  /**
   * 构造函数
   * @param entity 实体
   * @param options 选项
   */
  constructor(entity: Entity, options: Partial<AvatarPathOptions> = {}) {
    super(AvatarPathComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    this.autoStart = options.autoStart || false;
    this.debug = options.debug || false;
    this.smoothness = options.smoothness || 1.0;
    this.maxSpeed = options.maxSpeed || 10.0;
    this.minSpeed = options.minSpeed || 0.1;

    // 创建路径跟随组件
    this.pathFollowingComponent = new PathFollowingComponent(entity, {
      loop: false,
      speedMultiplier: 1.0,
      paused: !this.autoStart
    });
    entity.addComponent(this.pathFollowingComponent as any);

    // 监听路径跟随事件
    this.setupPathFollowingEvents();

    // 如果提供了初始路径数据，添加路径
    if (options.pathData) {
      this.addPath(options.pathData);
      if (this.autoStart) {
        this.setActivePath(options.pathData.id);
        this.startPath();
      }
    }
  }

  /**
   * 添加路径
   * @param pathData 路径数据
   * @returns 路径实例
   */
  public addPath(pathData: any): AvatarPath {
    const path = new AvatarPath(pathData);
    this.paths.set(path.id, path);

    // 监听路径事件
    path.on('pointAdded', (data) => {
      this.eventEmitter.emit('pathPointAdded', { pathId: path.id, ...data });
    });

    path.on('pointRemoved', (data) => {
      this.eventEmitter.emit('pathPointRemoved', { pathId: path.id, ...data });
    });

    path.on('pointUpdated', (data) => {
      this.eventEmitter.emit('pathPointUpdated', { pathId: path.id, ...data });
    });

    this.eventEmitter.emit('pathAdded', { path });

    if (this.debug) {
      console.log(`AvatarPathComponent: 添加路径 ${path.name} (${path.id})`);
    }

    return path;
  }

  /**
   * 移除路径
   * @param pathId 路径ID
   * @returns 是否成功移除
   */
  public removePath(pathId: string): boolean {
    const path = this.paths.get(pathId);
    if (!path) return false;

    // 如果是当前活动路径，先停止
    if (this.activePath && this.activePath.id === pathId) {
      this.stopPath();
      this.activePath = null;
    }

    this.paths.delete(pathId);
    this.eventEmitter.emit('pathRemoved', { pathId });

    if (this.debug) {
      console.log(`AvatarPathComponent: 移除路径 ${path.name} (${pathId})`);
    }

    return true;
  }

  /**
   * 获取路径
   * @param pathId 路径ID
   * @returns 路径实例
   */
  public getPath(pathId: string): AvatarPath | null {
    return this.paths.get(pathId) || null;
  }

  /**
   * 获取所有路径
   * @returns 路径列表
   */
  public getAllPaths(): AvatarPath[] {
    return Array.from(this.paths.values());
  }

  /**
   * 设置活动路径
   * @param pathId 路径ID
   * @returns 是否成功设置
   */
  public setActivePath(pathId: string): boolean {
    const path = this.paths.get(pathId);
    if (!path) {
      console.warn(`AvatarPathComponent: 路径 ${pathId} 不存在`);
      return false;
    }

    // 停止当前路径
    if (this.activePath) {
      this.stopPath();
    }

    this.activePath = path;
    
    // 设置路径跟随组件的路径
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setPath(path);
    }

    this.eventEmitter.emit('activePathChanged', { pathId, path });

    if (this.debug) {
      console.log(`AvatarPathComponent: 设置活动路径 ${path.name} (${pathId})`);
    }

    return true;
  }

  /**
   * 获取活动路径
   * @returns 活动路径
   */
  public getActivePath(): AvatarPath | null {
    return this.activePath;
  }

  /**
   * 开始路径
   */
  public startPath(): void {
    if (!this.activePath) {
      console.warn('AvatarPathComponent: 没有活动路径，无法开始');
      return;
    }

    if (!this.pathFollowingComponent) {
      console.warn('AvatarPathComponent: 路径跟随组件不存在');
      return;
    }

    // 验证路径
    const validation = this.activePath.validate();
    if (!validation.valid) {
      console.error('AvatarPathComponent: 路径验证失败', validation.errors);
      return;
    }

    this.pathFollowingComponent.start();

    if (this.debug) {
      console.log(`AvatarPathComponent: 开始路径 ${this.activePath.name}`);
    }
  }

  /**
   * 停止路径
   */
  public stopPath(): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.stop();
    }

    if (this.debug && this.activePath) {
      console.log(`AvatarPathComponent: 停止路径 ${this.activePath.name}`);
    }
  }

  /**
   * 暂停路径
   */
  public pausePath(): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.pause();
    }

    if (this.debug && this.activePath) {
      console.log(`AvatarPathComponent: 暂停路径 ${this.activePath.name}`);
    }
  }

  /**
   * 恢复路径
   */
  public resumePath(): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.resume();
    }

    if (this.debug && this.activePath) {
      console.log(`AvatarPathComponent: 恢复路径 ${this.activePath.name}`);
    }
  }

  /**
   * 重置路径
   */
  public resetPath(): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.reset();
    }

    if (this.debug && this.activePath) {
      console.log(`AvatarPathComponent: 重置路径 ${this.activePath.name}`);
    }
  }

  /**
   * 设置路径进度
   * @param progress 进度 (0-1)
   */
  public setPathProgress(progress: number): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setProgress(progress);
    }
  }

  /**
   * 获取路径进度
   * @returns 进度 (0-1)
   */
  public getPathProgress(): number {
    return this.pathFollowingComponent?.getProgress() || 0;
  }

  /**
   * 设置速度倍数
   * @param multiplier 速度倍数
   */
  public setSpeedMultiplier(multiplier: number): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setSpeedMultiplier(multiplier);
    }
  }

  /**
   * 获取速度倍数
   * @returns 速度倍数
   */
  public getSpeedMultiplier(): number {
    return this.pathFollowingComponent?.getSpeedMultiplier() || 1.0;
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setLoop(loop);
    }
  }

  /**
   * 获取路径平滑度
   * @returns 路径平滑度
   */
  public getSmoothness(): number {
    return this.smoothness;
  }

  /**
   * 设置路径平滑度
   * @param smoothness 路径平滑度
   */
  public setSmoothness(smoothness: number): void {
    this.smoothness = Math.max(0.1, Math.min(10.0, smoothness));
  }

  /**
   * 获取最大速度
   * @returns 最大速度
   */
  public getMaxSpeed(): number {
    return this.maxSpeed;
  }

  /**
   * 设置最大速度
   * @param maxSpeed 最大速度
   */
  public setMaxSpeed(maxSpeed: number): void {
    this.maxSpeed = Math.max(0.1, maxSpeed);
  }

  /**
   * 获取最小速度
   * @returns 最小速度
   */
  public getMinSpeed(): number {
    return this.minSpeed;
  }

  /**
   * 设置最小速度
   * @param minSpeed 最小速度
   */
  public setMinSpeed(minSpeed: number): void {
    this.minSpeed = Math.max(0.01, Math.min(this.maxSpeed, minSpeed));
  }

  /**
   * 获取当前位置
   * @returns 当前位置
   */
  public getCurrentPosition(): THREE.Vector3 | null {
    return this.pathFollowingComponent?.getCurrentPosition() || null;
  }

  /**
   * 获取当前旋转
   * @returns 当前旋转
   */
  public getCurrentRotation(): THREE.Quaternion | null {
    return this.pathFollowingComponent?.getCurrentRotation() || null;
  }

  /**
   * 获取当前速度
   * @returns 当前速度
   */
  public getCurrentSpeed(): number {
    return this.pathFollowingComponent?.getCurrentSpeed() || 0;
  }

  /**
   * 获取当前动画
   * @returns 当前动画名称
   */
  public getCurrentAnimation(): string {
    return this.pathFollowingComponent?.getCurrentAnimation() || '';
  }

  /**
   * 设置路径跟随事件监听
   */
  private setupPathFollowingEvents(): void {
    if (!this.pathFollowingComponent) return;

    this.pathFollowingComponent.addEventListener('pathStarted', (data) => {
      this.eventEmitter.emit('pathStarted', data);
    });

    this.pathFollowingComponent.addEventListener('pathCompleted', (data) => {
      this.eventEmitter.emit('pathCompleted', data);
    });

    this.pathFollowingComponent.addEventListener('waypointReached', (data) => {
      this.eventEmitter.emit('waypointReached', data);
    });

    this.pathFollowingComponent.addEventListener('pathPaused', (data) => {
      this.eventEmitter.emit('pathPaused', data);
    });

    this.pathFollowingComponent.addEventListener('pathResumed', (data) => {
      this.eventEmitter.emit('pathResumed', data);
    });

    this.pathFollowingComponent.addEventListener('triggerActivated', (data) => {
      this.eventEmitter.emit('triggerActivated', data);
    });
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量
   */
  public update(_deltaTime: number): void {
    // 路径跟随组件会自动更新
    // 这里可以添加额外的逻辑，如调试信息显示等

    if (this.debug && this.activePath && this.pathFollowingComponent) {
      const progress = this.pathFollowingComponent.getProgress();
      const position = this.pathFollowingComponent.getCurrentPosition();
      const speed = this.pathFollowingComponent.getCurrentSpeed();

      // 可以在这里添加调试信息的显示
      if (position) {
        console.log(`Path Progress: ${(progress * 100).toFixed(1)}%, Position: ${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}, Speed: ${speed.toFixed(2)}`);
      }
    }
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.stopPath();
    
    // 清理路径
    this.paths.clear();
    this.activePath = null;

    // 移除路径跟随组件
    if (this.pathFollowingComponent && this.entity) {
      this.entity.removeComponent(PathFollowingComponent.TYPE);
      this.pathFollowingComponent = null;
    }

    this.eventEmitter.removeAllListeners();
    super.dispose();
  }
}
