# 数字人路径编辑系统 API 文档

## 概述

本文档描述了数字人路径编辑系统的RESTful API和WebSocket API接口。API遵循RESTful设计原则，使用JSON格式进行数据交换。

## 基础信息

- **Base URL**: `https://api.example.com/v1`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含认证令牌：

```http
Authorization: Bearer <your-access-token>
Content-Type: application/json
```

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      {
        "field": "name",
        "message": "路径名称不能为空"
      }
    ]
  },
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 数据模型

### AvatarPath (路径)

```json
{
  "id": "string",
  "name": "string",
  "avatarId": "string",
  "projectId": "string",
  "sceneId": "string",
  "points": [PathPoint],
  "loopMode": "none|loop|pingpong",
  "interpolation": "linear|smooth|bezier|spline",
  "totalDuration": "number",
  "totalLength": "number",
  "enabled": "boolean",
  "metadata": {
    "createdAt": "string",
    "updatedAt": "string",
    "creator": "string",
    "version": "number",
    "description": "string",
    "tags": ["string"],
    "category": "string"
  }
}
```

### PathPoint (路径点)

```json
{
  "id": "string",
  "position": {
    "x": "number",
    "y": "number",
    "z": "number"
  },
  "waitTime": "number",
  "speed": "number",
  "animation": "string",
  "lookAt": {
    "x": "number",
    "y": "number",
    "z": "number"
  },
  "triggers": [PathTrigger],
  "userData": "object"
}
```

### PathTrigger (路径触发器)

```json
{
  "type": "dialogue|animation|sound|event|custom",
  "data": "object",
  "condition": "string",
  "delay": "number",
  "once": "boolean"
}
```

## RESTful API

### 路径管理

#### 创建路径

```http
POST /api/avatar-paths
```

**请求体:**
```json
{
  "name": "巡逻路径",
  "avatarId": "avatar_001",
  "projectId": "project_001",
  "sceneId": "scene_001",
  "points": [
    {
      "position": { "x": 0, "y": 0, "z": 0 },
      "waitTime": 2,
      "speed": 1.5,
      "animation": "idle"
    },
    {
      "position": { "x": 10, "y": 0, "z": 0 },
      "waitTime": 0,
      "speed": 2.0,
      "animation": "walk"
    }
  ],
  "loopMode": "loop",
  "interpolation": "smooth",
  "metadata": {
    "description": "数字人巡逻路径",
    "tags": ["patrol", "security"],
    "category": "security"
  }
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "id": "path_12345",
    "name": "巡逻路径",
    // ... 完整的路径数据
  },
  "message": "路径创建成功"
}
```

#### 获取路径列表

```http
GET /api/avatar-paths
```

**查询参数:**
- `projectId` (required): 项目ID
- `avatarId` (optional): 数字人ID
- `sceneId` (optional): 场景ID
- `keyword` (optional): 搜索关键词
- `tags` (optional): 标签过滤，逗号分隔
- `category` (optional): 分类过滤
- `enabled` (optional): 启用状态过滤
- `page` (optional): 页码，默认1
- `limit` (optional): 每页数量，默认20
- `sortBy` (optional): 排序字段，默认updatedAt
- `sortOrder` (optional): 排序方向，asc/desc，默认desc

**示例请求:**
```http
GET /api/avatar-paths?projectId=project_001&page=1&limit=10&sortBy=name&sortOrder=asc
```

**响应:**
```json
{
  "success": true,
  "data": {
    "paths": [
      {
        "id": "path_12345",
        "name": "巡逻路径",
        // ... 路径数据
      }
    ],
    "total": 25,
    "page": 1,
    "limit": 10,
    "totalPages": 3
  },
  "message": "获取路径列表成功"
}
```

#### 获取路径详情

```http
GET /api/avatar-paths/{pathId}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "id": "path_12345",
    "name": "巡逻路径",
    // ... 完整的路径数据
  },
  "message": "获取路径详情成功"
}
```

#### 更新路径

```http
PUT /api/avatar-paths/{pathId}
```

**请求体:**
```json
{
  "name": "更新后的路径名称",
  "points": [
    // 更新后的路径点数组
  ],
  "loopMode": "pingpong",
  "enabled": false,
  "metadata": {
    "description": "更新后的描述",
    "tags": ["updated", "test"]
  }
}
```

#### 删除路径

```http
DELETE /api/avatar-paths/{pathId}
```

**响应:**
```json
{
  "success": true,
  "data": null,
  "message": "路径删除成功"
}
```

#### 克隆路径

```http
POST /api/avatar-paths/{pathId}/clone
```

**请求体:**
```json
{
  "name": "克隆的路径名称"
}
```

#### 验证路径

```http
POST /api/avatar-paths/{pathId}/validate
```

**响应:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "score": 95,
    "errors": [],
    "warnings": [
      {
        "type": "warning",
        "code": "SPEED_TOO_FAST",
        "message": "路径点2的速度可能过快",
        "pointIndex": 1,
        "severity": "medium"
      }
    ],
    "suggestions": [
      {
        "type": "suggestion",
        "code": "OPTIMIZE_PATH",
        "message": "建议简化路径以提高性能",
        "severity": "low"
      }
    ],
    "performance": {
      "complexity": 25,
      "memoryUsage": 2048,
      "renderCost": 15,
      "pathLength": 50.5,
      "estimatedFPS": 58
    }
  },
  "message": "路径验证完成"
}
```

#### 导出路径

```http
GET /api/avatar-paths/{pathId}/export
```

**响应:** 直接返回JSON文件下载

#### 导入路径

```http
POST /api/avatar-paths/import
```

**请求体:**
```json
{
  "projectId": "project_001",
  "pathData": {
    // 导入的路径数据
  }
}
```

#### 批量操作

```http
POST /api/avatar-paths/batch
```

**请求体:**
```json
{
  "pathIds": ["path_001", "path_002", "path_003"],
  "operation": "enable|disable|delete|updateCategory",
  "data": {
    "category": "security"
  }
}
```

### 统计信息

#### 获取项目路径统计

```http
GET /api/avatar-paths/projects/{projectId}/statistics
```

**响应:**
```json
{
  "success": true,
  "data": {
    "totalPaths": 25,
    "enabledPaths": 20,
    "disabledPaths": 5,
    "averageDuration": 45.2,
    "averageLength": 120.5,
    "totalPoints": 150,
    "pathsByLoopMode": {
      "none": 10,
      "loop": 12,
      "pingpong": 3
    },
    "pathsByInterpolation": {
      "linear": 8,
      "smooth": 12,
      "bezier": 3,
      "spline": 2
    },
    "pathsByCategory": {
      "security": 10,
      "guide": 8,
      "entertainment": 7
    }
  },
  "message": "获取统计信息成功"
}
```

## WebSocket API

### 连接

```javascript
const socket = io('wss://api.example.com', {
  auth: {
    token: 'your-access-token'
  }
});
```

### 事件

#### 加入路径编辑会话

**发送:**
```javascript
socket.emit('joinPathEdit', {
  pathId: 'path_12345',
  userId: 'user_001',
  userName: '张三'
});
```

**接收:**
```javascript
socket.on('pathEditJoined', (data) => {
  console.log('加入编辑会话成功', data);
  // data.pathId, data.path, data.activeSessions, data.operationHistory
});
```

#### 离开路径编辑会话

**发送:**
```javascript
socket.emit('leavePathEdit', {
  pathId: 'path_12345'
});
```

#### 路径操作

**发送:**
```javascript
socket.emit('pathOperation', {
  type: 'addPoint',
  pathId: 'path_12345',
  data: {
    point: {
      position: { x: 5, y: 0, z: 5 },
      waitTime: 1,
      speed: 1.5,
      animation: 'walk'
    }
  }
});
```

**接收:**
```javascript
socket.on('pathOperation', (operation) => {
  console.log('收到路径操作', operation);
  // 应用其他用户的操作
});
```

#### 光标移动

**发送:**
```javascript
socket.emit('cursorMove', {
  pathId: 'path_12345',
  cursor: { x: 10, y: 0, z: 5 }
});
```

**接收:**
```javascript
socket.on('cursorMove', (data) => {
  console.log('用户光标移动', data);
  // data.userId, data.cursor
});
```

#### 选择变化

**发送:**
```javascript
socket.emit('selectionChange', {
  pathId: 'path_12345',
  selection: { pointIndex: 2 }
});
```

**接收:**
```javascript
socket.on('selectionChange', (data) => {
  console.log('用户选择变化', data);
  // data.userId, data.selection
});
```

#### 用户状态事件

```javascript
// 用户加入
socket.on('userJoined', (data) => {
  console.log('用户加入', data);
  // data.userId, data.userName, data.joinedAt
});

// 用户离开
socket.on('userLeft', (data) => {
  console.log('用户离开', data);
  // data.userId
});

// 路径更新
socket.on('pathUpdated', (data) => {
  console.log('路径已更新', data);
  // data.pathId, data.path, data.operations
});
```

## 错误代码

### 通用错误

- `INVALID_REQUEST`: 请求格式错误
- `UNAUTHORIZED`: 未授权访问
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `CONFLICT`: 资源冲突
- `RATE_LIMIT_EXCEEDED`: 请求频率超限

### 路径相关错误

- `PATH_NOT_FOUND`: 路径不存在
- `PATH_VALIDATION_FAILED`: 路径验证失败
- `INVALID_PATH_DATA`: 路径数据格式错误
- `PATH_NAME_EXISTS`: 路径名称已存在
- `INSUFFICIENT_POINTS`: 路径点数量不足
- `INVALID_POINT_DATA`: 路径点数据无效

### 协作相关错误

- `SESSION_NOT_FOUND`: 编辑会话不存在
- `USER_ALREADY_JOINED`: 用户已加入会话
- `MAX_USERS_EXCEEDED`: 超过最大用户数限制
- `OPERATION_CONFLICT`: 操作冲突
- `SYNC_FAILED`: 同步失败

## 限制和配额

### 请求限制

- **API请求频率**: 每分钟1000次
- **WebSocket连接**: 每用户最多10个连接
- **文件上传大小**: 最大10MB
- **路径点数量**: 每个路径最多1000个点

### 数据限制

- **路径名称长度**: 最大100字符
- **描述长度**: 最大1000字符
- **标签数量**: 每个路径最多20个标签
- **触发器数量**: 每个路径点最多10个触发器

## SDK 示例

### JavaScript SDK

```javascript
import { AvatarPathAPI } from '@avatar-path/sdk';

const api = new AvatarPathAPI({
  baseURL: 'https://api.example.com/v1',
  token: 'your-access-token'
});

// 创建路径
const path = await api.paths.create({
  name: '测试路径',
  avatarId: 'avatar_001',
  projectId: 'project_001',
  points: [
    { position: { x: 0, y: 0, z: 0 }, speed: 1.5, animation: 'idle' },
    { position: { x: 10, y: 0, z: 0 }, speed: 2.0, animation: 'walk' }
  ]
});

// 获取路径列表
const paths = await api.paths.list({
  projectId: 'project_001',
  page: 1,
  limit: 20
});

// 实时协作
const collaboration = api.collaboration.join('path_12345');
collaboration.on('userJoined', (user) => {
  console.log('用户加入:', user);
});
```

### Python SDK

```python
from avatar_path_sdk import AvatarPathAPI

api = AvatarPathAPI(
    base_url='https://api.example.com/v1',
    token='your-access-token'
)

# 创建路径
path = api.paths.create({
    'name': '测试路径',
    'avatarId': 'avatar_001',
    'projectId': 'project_001',
    'points': [
        {'position': {'x': 0, 'y': 0, 'z': 0}, 'speed': 1.5, 'animation': 'idle'},
        {'position': {'x': 10, 'y': 0, 'z': 0}, 'speed': 2.0, 'animation': 'walk'}
    ]
})

# 获取路径列表
paths = api.paths.list(project_id='project_001', page=1, limit=20)
```

## 更新日志

### v1.2.0 (2023-12-01)
- 新增路径验证API
- 支持批量操作
- 优化WebSocket性能

### v1.1.0 (2023-11-01)
- 新增路径统计API
- 支持路径导入导出
- 新增触发器功能

### v1.0.0 (2023-10-01)
- 初始版本发布
- 基础路径CRUD操作
- WebSocket实时协作

---

*如有疑问，请联系技术支持团队。*
