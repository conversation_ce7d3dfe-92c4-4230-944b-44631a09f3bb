/**
 * RAG场景示例
 * 展示如何在场景中创建和使用RAG应用
 */
import { Scene } from '../scene/Scene';
import { World } from '../core/World';
import { Engine } from '../core/Engine';
import { SceneRAGApplicationFactory } from '../scene/SceneRAGApplicationFactory';
import { SceneRAGComponent } from '../scene/components/SceneRAGComponent';
import type { Entity } from '../core/Entity';

/**
 * RAG场景示例类
 */
export class RAGSceneExample {
  /** 引擎实例 */
  private engine: Engine;

  /** 世界实例 */
  private world: World;

  /** 场景实例 */
  private scene: Scene;

  /** RAG应用实体列表 */
  private ragApplications: Entity[] = [];

  /**
   * 构造函数
   */
  constructor() {
    this.engine = new Engine();
    this.world = new World(this.engine);
    this.scene = new Scene('RAG示例场景');
  }

  /**
   * 初始化示例场景
   */
  public async initialize(): Promise<void> {
    try {
      console.log('初始化RAG示例场景...');

      // 初始化引擎和世界
      this.engine.initialize();
      this.world.initialize();

      // 将场景添加到世界
      this.world.addScene(this.scene);
      this.world.setActiveScene(this.scene);

      // 创建医疗咨询应用
      await this.createMedicalConsultationApp();

      // 创建教育培训应用
      await this.createEducationTrainingApp();

      // 创建客服助手应用
      await this.createCustomerServiceApp();

      // 设置交互事件监听
      this.setupInteractionListeners();

      console.log('RAG示例场景初始化完成');
    } catch (error) {
      console.error('RAG示例场景初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建医疗咨询应用
   */
  private async createMedicalConsultationApp(): Promise<void> {
    console.log('创建医疗咨询应用...');

    const medicalApp = await SceneRAGApplicationFactory.createMedicalConsultationApp(
      this.scene,
      '智能医疗咨询助手',
      'medical_knowledge_base_001',
      { x: -5, y: 0, z: 0 }
    );

    this.ragApplications.push(medicalApp);
    console.log('医疗咨询应用创建成功');
  }

  /**
   * 创建教育培训应用
   */
  private async createEducationTrainingApp(): Promise<void> {
    console.log('创建教育培训应用...');

    const educationApp = await SceneRAGApplicationFactory.createEducationTrainingApp(
      this.scene,
      '智能教育培训助手',
      'education_knowledge_base_001',
      { x: 0, y: 0, z: 0 }
    );

    this.ragApplications.push(educationApp);
    console.log('教育培训应用创建成功');
  }

  /**
   * 创建客服助手应用
   */
  private async createCustomerServiceApp(): Promise<void> {
    console.log('创建客服助手应用...');

    const customerServiceApp = await SceneRAGApplicationFactory.createCustomerServiceApp(
      this.scene,
      '智能客服助手',
      'customer_service_knowledge_base_001',
      { x: 5, y: 0, z: 0 }
    );

    this.ragApplications.push(customerServiceApp);
    console.log('客服助手应用创建成功');
  }

  /**
   * 设置交互事件监听
   */
  private setupInteractionListeners(): void {
    const interactionManager = this.scene.getInteractionManager();

    // 监听交互开始事件
    interactionManager.on('interactionStarted', (event) => {
      console.log(`用户 ${event.userId} 开始与数字人 ${event.avatarId} 交互`);
      console.log(`会话ID: ${event.sessionId}`);
    });

    // 监听交互结束事件
    interactionManager.on('interactionEnded', (event) => {
      console.log(`用户 ${event.userId} 结束与数字人 ${event.avatarId} 的交互`);
    });

    // 监听消息处理事件
    interactionManager.on('interactionProcessed', (event) => {
      console.log(`处理交互事件: ${event.type}, 数字人: ${event.avatarId}`);
    });

    // 监听数字人管理器事件
    const avatarManager = this.scene.getAvatarManager();

    avatarManager.on('conversationStarted', (event) => {
      console.log(`对话开始: 数字人 ${event.avatarId}, 会话 ${event.sessionId}`);
    });

    avatarManager.on('conversationEnded', (event) => {
      console.log(`对话结束: 数字人 ${event.avatarId}, 会话 ${event.sessionId}`);
    });

    avatarManager.on('messageProcessed', (event) => {
      console.log(`消息处理完成: ${event.message}`);
    });
  }

  /**
   * 模拟用户交互
   */
  public async simulateUserInteraction(): Promise<void> {
    console.log('开始模拟用户交互...');

    const interactionManager = this.scene.getInteractionManager();

    // 模拟用户移动到医疗咨询区域
    console.log('用户移动到医疗咨询区域...');
    interactionManager.updateUserPosition('user_001', { x: -5, y: 0, z: 1 });

    // 等待一段时间
    await this.delay(1000);

    // 模拟点击交互
    console.log('用户点击医疗数字人...');
    await interactionManager.handleClickInteraction({ x: -5, y: 0, z: 0 }, 'user_001');

    // 等待一段时间
    await this.delay(2000);

    // 模拟语音交互
    console.log('用户发送语音消息...');
    await interactionManager.handleVoiceInteraction('我最近感觉头痛，请问可能是什么原因？', 'user_001');

    // 等待一段时间
    await this.delay(3000);

    // 结束交互
    console.log('结束交互...');
    await interactionManager.endInteraction('user_001');

    // 移动到教育培训区域
    console.log('用户移动到教育培训区域...');
    interactionManager.updateUserPosition('user_001', { x: 0, y: 0, z: 1 });

    await this.delay(1000);

    // 与教育数字人交互
    console.log('用户与教育数字人交互...');
    await interactionManager.handleVoiceInteraction('请介绍一下机器学习的基本概念', 'user_001');

    await this.delay(3000);

    await interactionManager.endInteraction('user_001');

    console.log('用户交互模拟完成');
  }

  /**
   * 获取场景统计信息
   */
  public getSceneStatistics(): any {
    const avatarManager = this.scene.getAvatarManager();
    const interactionManager = this.scene.getInteractionManager();

    const activeAvatars = avatarManager.getActiveAvatars();
    const conversationAvatars = avatarManager.getConversationAvatars();
    const interactionZones = interactionManager.getInteractionZones();
    const userStates = interactionManager.getUserStates();

    return {
      scene: {
        name: this.scene.getName(),
        entityCount: this.scene.getEntities().length,
      },
      ragApplications: {
        total: this.ragApplications.length,
        active: this.ragApplications.filter(app => {
          const ragComponent = app.getComponent('SceneRAGComponent') as unknown as SceneRAGComponent;
          return ragComponent && ragComponent.getState && ragComponent.getState().status === 'running';
        }).length,
      },
      avatars: {
        total: activeAvatars.length,
        inConversation: conversationAvatars.length,
        available: activeAvatars.length - conversationAvatars.length,
      },
      interactions: {
        zones: interactionZones.length,
        activeUsers: userStates.filter(user => user.isInteracting).length,
        totalUsers: userStates.length,
      },
    };
  }

  /**
   * 更新场景
   */
  public update(deltaTime: number): void {
    this.scene.update(deltaTime);
  }

  /**
   * 启动所有RAG应用
   */
  public async startAllApplications(): Promise<void> {
    console.log('启动所有RAG应用...');

    for (const app of this.ragApplications) {
      const ragComponent = app.getComponent('SceneRAGComponent') as unknown as SceneRAGComponent;
      if (ragComponent && ragComponent.start) {
        try {
          await ragComponent.start();
          console.log(`RAG应用 "${ragComponent.getConfig().name}" 启动成功`);
        } catch (error) {
          console.error(`RAG应用启动失败:`, error);
        }
      }
    }

    console.log('所有RAG应用启动完成');
  }

  /**
   * 停止所有RAG应用
   */
  public async stopAllApplications(): Promise<void> {
    console.log('停止所有RAG应用...');

    for (const app of this.ragApplications) {
      const ragComponent = app.getComponent('SceneRAGComponent') as unknown as SceneRAGComponent;
      if (ragComponent && ragComponent.stop) {
        try {
          await ragComponent.stop();
          console.log(`RAG应用 "${ragComponent.getConfig().name}" 停止成功`);
        } catch (error) {
          console.error(`RAG应用停止失败:`, error);
        }
      }
    }

    console.log('所有RAG应用停止完成');
  }

  /**
   * 添加自定义RAG应用
   */
  public async addCustomRAGApplication(
    name: string,
    knowledgeBaseId: string,
    position: { x: number; y: number; z: number },
    templateId?: string
  ): Promise<Entity> {
    console.log(`添加自定义RAG应用: ${name}`);

    const app = await SceneRAGApplicationFactory.createRAGApplication(this.scene, {
      name,
      knowledgeBaseId,
      templateId,
      scenePosition: position,
    });

    this.ragApplications.push(app);
    console.log(`自定义RAG应用 "${name}" 添加成功`);

    return app;
  }

  /**
   * 移除RAG应用
   */
  public async removeRAGApplication(entity: Entity): Promise<void> {
    const ragComponent = entity.getComponent('SceneRAGComponent') as unknown as SceneRAGComponent;
    if (!ragComponent || !ragComponent.getConfig) {
      throw new Error('实体不包含RAG组件');
    }

    const appName = ragComponent.getConfig().name;
    console.log(`移除RAG应用: ${appName}`);

    // 停止应用
    if (ragComponent.stop) {
      await ragComponent.stop();
    }

    // 从场景中移除
    this.scene.removeEntity(entity);

    // 从列表中移除
    const index = this.ragApplications.indexOf(entity);
    if (index > -1) {
      this.ragApplications.splice(index, 1);
    }

    console.log(`RAG应用 "${appName}" 移除成功`);
  }

  /**
   * 获取场景实例
   */
  public getScene(): Scene {
    return this.scene;
  }

  /**
   * 获取RAG应用列表
   */
  public getRAGApplications(): Entity[] {
    return [...this.ragApplications];
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 销毁示例场景
   */
  public dispose(): void {
    console.log('销毁RAG示例场景...');

    // 停止所有应用
    this.stopAllApplications().catch(console.error);

    // 销毁场景
    this.scene.dispose();

    // 清理资源
    this.ragApplications.length = 0;

    console.log('RAG示例场景销毁完成');
  }
}

/**
 * 创建并运行RAG场景示例
 */
export async function runRAGSceneExample(): Promise<RAGSceneExample> {
  const example = new RAGSceneExample();
  
  try {
    await example.initialize();
    await example.startAllApplications();
    
    // 打印统计信息
    console.log('场景统计信息:', example.getSceneStatistics());
    
    // 模拟用户交互
    await example.simulateUserInteraction();
    
    return example;
  } catch (error) {
    console.error('RAG场景示例运行失败:', error);
    example.dispose();
    throw error;
  }
}
